import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';

interface ContentItem {
  id: number;
  page_name: string;
  section_name: string;
  content_type: string;
  content_key: string;
  content_value: string;
  updated_at: string;
}

interface UseContentReturn {
  content: Record<string, string>;
  loading: boolean;
  error: string | null;
  updateContent: (key: string, value: string) => Promise<boolean>;
  refreshContent: () => Promise<void>;
}

export const useContent = (pageName: string, sectionName?: string): UseContentReturn => {
  const [content, setContent] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchContent = async () => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase
        .from('site_content')
        .select('*')
        .eq('page_name', pageName);

      if (sectionName) {
        query = query.eq('section_name', sectionName);
      }

      const { data, error: fetchError } = await query;

      if (fetchError) {
        throw fetchError;
      }

      // Convertir les données en objet clé-valeur
      const contentMap: Record<string, string> = {};
      data?.forEach((item: ContentItem) => {
        contentMap[item.content_key] = item.content_value || '';
      });

      setContent(contentMap);
    } catch (err: any) {
      console.error('Erreur lors du chargement du contenu:', err);
      setError(err.message || 'Erreur lors du chargement du contenu');
    } finally {
      setLoading(false);
    }
  };

  const updateContent = async (key: string, value: string): Promise<boolean> => {
    try {
      const { error: updateError } = await supabase
        .from('site_content')
        .update({ content_value: value })
        .eq('content_key', key)
        .eq('page_name', pageName);

      if (updateError) {
        throw updateError;
      }

      // Mettre à jour le state local
      setContent(prev => ({ ...prev, [key]: value }));
      return true;
    } catch (err: any) {
      console.error('Erreur lors de la mise à jour du contenu:', err);
      setError(err.message || 'Erreur lors de la mise à jour');
      return false;
    }
  };

  const refreshContent = async () => {
    await fetchContent();
  };

  useEffect(() => {
    fetchContent();
  }, [pageName, sectionName]);

  return {
    content,
    loading,
    error,
    updateContent,
    refreshContent
  };
};

// Hook spécialisé pour le contenu global (footer, header, etc.)
export const useGlobalContent = (sectionName: string) => {
  return useContent('global', sectionName);
};

// Hook pour obtenir une valeur de contenu spécifique avec fallback
export const useContentValue = (pageName: string, sectionName: string, key: string, fallback: string = '') => {
  const { content, loading } = useContent(pageName, sectionName);
  return {
    value: content[key] || fallback,
    loading
  };
};