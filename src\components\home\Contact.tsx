import React, { useState } from 'react';
import Button from '../ui/Button';
import { Phone, Download } from 'lucide-react';
import { sendPDFToWhatsApp, type DevisData } from '../../utils/pdfGenerator';

interface FormValues {
  name: string;
  email: string;
  phone: string;
  eventType: string;
  message: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  message?: string;
}

const initialValues: FormValues = {
  name: '',
  email: '',
  phone: '',
  eventType: '',
  message: ''
};

const Contact = () => {
  const [values, setValues] = useState<FormValues>(initialValues);
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setValues({ ...values, [name]: value });
    // Clear error when field is edited
    if (errors[name as keyof FormErrors]) {
      setErrors({ ...errors, [name]: undefined });
    }
  };

  const validate = () => {
    const newErrors: FormErrors = {};

    if (!values.name.trim()) {
      newErrors.name = 'Le nom est requis';
    }

    if (!values.email.trim()) {
      newErrors.email = 'L\'email est requis';
    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(values.email)) {
      newErrors.email = 'Adresse email invalide';
    }

    if (values.phone && !/^[0-9\s+()-]{8,15}$/.test(values.phone)) {
      newErrors.phone = 'Numéro de téléphone invalide';
    }

    if (!values.message.trim()) {
      newErrors.message = 'Le message est requis';
    }

    return newErrors;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const newErrors = validate();
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsSubmitting(true);

    // Préparer les données pour le PDF
    const devisData: DevisData = {
      name: values.name,
      email: values.email,
      phone: values.phone,
      eventType: values.eventType,
      message: values.message,
      date: new Date().toLocaleDateString('fr-FR')
    };

    // Simuler un délai puis générer le PDF et ouvrir WhatsApp
    setTimeout(() => {
      setIsSubmitting(false);
      setSubmitSuccess(true);
      setValues(initialValues);

      // Générer le PDF et ouvrir WhatsApp
      setTimeout(() => {
        sendPDFToWhatsApp(devisData);
      }, 1000);

      // Reset success message after 8 seconds (plus de temps pour le téléchargement)
      setTimeout(() => {
        setSubmitSuccess(false);
      }, 8000);
    }, 1500);
  };

  const eventTypes = [
    { value: '', label: 'Sélectionnez le type d\'événement' },
    { value: 'enfant', label: 'Événement pour enfant' },
    { value: 'adulte', label: 'Événement pour adulte' },
    { value: 'mariage', label: 'Mariage' },
    { value: 'conference', label: 'Conférence/Événement professionnel' },
    { value: 'autre', label: 'Autre' }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto bg-white rounded-3xl shadow-xl overflow-hidden">
          <div className="flex flex-col md:flex-row">
            <div className="md:w-2/5 bg-primary-600 p-8 md:p-12 text-white">
              <h2 className="text-3xl font-display mb-6">Contactez-nous</h2>
              <p className="mb-8">
                Parlez-nous de votre événement et laissez-nous vous aider à créer une décoration magique qui émerveillera vos invités.
              </p>

              <div className="space-y-6">
                <div className="flex items-start">
                  <Phone size={20} className="mr-3 mt-1" />
                  <div>
                    <p className="font-medium">Téléphone</p>
                    <a href="tel:+2250574144673" className="block hover:underline">
                      057 414 46 73
                    </a>
                    <p className="text-sm text-primary-100 mt-1">
                      WhatsApp: +225 05 74 14 46 73
                    </p>
                  </div>
                </div>

                <div className="mt-8">
                  <p className="font-medium mb-2">Suivez-nous</p>
                  <a
                    href="https://www.facebook.com/laboutiquedeminnieci"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block bg-white/10 hover:bg-white/20 rounded-full p-2 transition-colors"
                    aria-label="Facebook"
                    title="Suivez-nous sur Facebook"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-facebook">
                      <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <div className="md:w-3/5 p-8 md:p-12">
              {submitSuccess ? (
                <div className="h-full flex flex-col items-center justify-center text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <Download size={24} className="text-blue-600" />
                  </div>
                  <h3 className="text-2xl font-bold text-secondary-800 mb-2">PDF généré avec succès !</h3>
                  <p className="text-secondary-600 mb-4">
                    Votre devis PDF a été téléchargé automatiquement. WhatsApp va s'ouvrir pour que vous puissiez l'envoyer en pièce jointe.
                  </p>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <p className="text-sm text-yellow-800">
                      📎 <strong>Instructions :</strong> Joignez le fichier PDF téléchargé à votre message WhatsApp pour un devis complet et professionnel.
                    </p>
                  </div>
                </div>
              ) : (
                <form onSubmit={handleSubmit}>
                  <h3 className="text-2xl font-bold text-secondary-800 mb-6">Envoyez-nous un message</h3>

                  <div className="space-y-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-secondary-700 mb-1">
                        Nom complet*
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={values.name}
                        onChange={handleChange}
                        className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:outline-none transition-colors ${
                          errors.name
                            ? 'border-red-300 focus:ring-red-200'
                            : 'border-secondary-300 focus:ring-primary-200 focus:border-primary-400'
                        }`}
                        placeholder="Votre nom"
                      />
                      {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-secondary-700 mb-1">
                          Email*
                        </label>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={values.email}
                          onChange={handleChange}
                          className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:outline-none transition-colors ${
                            errors.email
                              ? 'border-red-300 focus:ring-red-200'
                              : 'border-secondary-300 focus:ring-primary-200 focus:border-primary-400'
                          }`}
                          placeholder="<EMAIL>"
                        />
                        {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
                      </div>

                      <div>
                        <label htmlFor="phone" className="block text-sm font-medium text-secondary-700 mb-1">
                          Téléphone
                        </label>
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={values.phone}
                          onChange={handleChange}
                          className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:outline-none transition-colors ${
                            errors.phone
                              ? 'border-red-300 focus:ring-red-200'
                              : 'border-secondary-300 focus:ring-primary-200 focus:border-primary-400'
                          }`}
                          placeholder="Votre numéro"
                        />
                        {errors.phone && <p className="mt-1 text-sm text-red-600">{errors.phone}</p>}
                      </div>
                    </div>

                    <div>
                      <label htmlFor="eventType" className="block text-sm font-medium text-secondary-700 mb-1">
                        Type d'événement
                      </label>
                      <select
                        id="eventType"
                        name="eventType"
                        value={values.eventType}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-200 focus:border-primary-400 focus:outline-none transition-colors"
                      >
                        {eventTypes.map((type) => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label htmlFor="message" className="block text-sm font-medium text-secondary-700 mb-1">
                        Message*
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        value={values.message}
                        onChange={handleChange}
                        rows={4}
                        className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:outline-none transition-colors ${
                          errors.message
                            ? 'border-red-300 focus:ring-red-200'
                            : 'border-secondary-300 focus:ring-primary-200 focus:border-primary-400'
                        }`}
                        placeholder="Décrivez votre événement et vos besoins en décoration..."
                      />
                      {errors.message && <p className="mt-1 text-sm text-red-600">{errors.message}</p>}
                    </div>

                    <div className="pt-2">
                      <Button
                        type="submit"
                        className="w-full"
                      >
                        {isSubmitting ? 'Génération du PDF...' : 'Générer le devis PDF'}
                      </Button>
                    </div>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>

        {/* WhatsApp Call To Action */}
        <div className="mt-12 text-center">
          <a
            href="https://wa.me/2250574144673"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-6 py-3 bg-green-500 hover:bg-green-600 text-white rounded-full transition-colors shadow-md"
            aria-label="Contactez-nous sur WhatsApp"
            title="Contactez-nous sur WhatsApp au +225 05 74 14 46 73"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/>
            </svg>
            Contactez-nous sur WhatsApp
          </a>
        </div>
      </div>
    </section>
  );
};

export default Contact;