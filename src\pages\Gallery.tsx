import React from 'react';
import MediaGallery from '../components/gallery/MediaGallery';

const Gallery = () => {
  return (
    <div className="pt-20">
      <div className="bg-primary-100 py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-display text-primary-700 mb-4">Galerie Multimédia</h1>
          <p className="text-lg text-secondary-700 max-w-2xl mx-auto">
            Explorez nos réalisations en photos et vidéos pour tous types d'événements.
          </p>
        </div>
      </div>
      
      <MediaGallery />
      
      <div className="bg-primary-50 py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-display text-primary-700 mb-6">Vous avez un projet ?</h2>
          <p className="text-lg text-secondary-700 max-w-2xl mx-auto mb-8">
            Contactez-nous pour discuter de votre événement et découvrir comment nous pouvons le rendre inoubliable.
          </p>
          <a 
            href="/contact" 
            className="inline-block bg-primary-500 hover:bg-primary-600 text-white px-8 py-3 rounded-full transition-colors font-medium"
          >
            Demander un devis
          </a>
        </div>
      </div>
    </div>
  );
};

export default Gallery;
