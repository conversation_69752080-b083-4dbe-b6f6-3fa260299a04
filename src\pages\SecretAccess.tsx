import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ADMIN_TOKEN } from '../lib/adminAccess';

const SecretAccess = () => {
  const [accessCode, setAccessCode] = useState('');
  const navigate = useNavigate();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    console.log("Code d'accès soumis:", accessCode);
    
    // Code secret personnalisé que vous seul connaissez
    if (accessCode === 'minnie2024') {
      console.log("Code correct, redirection vers:", `/secure-admin/${ADMIN_TOKEN}`);
      navigate(`/secure-admin/${ADMIN_TOKEN}`);
    } else {
      console.log("Code incorrect, redirection vers la page d'accueil");
      // Rediriger vers la page d'accueil en cas de code incorrect
      navigate('/');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-10 bg-white rounded-xl shadow-md">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Accès Propriétaire
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="access-code" className="sr-only">
              Code d'accès
            </label>
            <input
              id="access-code"
              name="code"
              type="password"
              required
              className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
              placeholder="Code d'accès"
              value={accessCode}
              onChange={(e) => setAccessCode(e.target.value)}
            />
          </div>
          <div>
            <button
              type="submit"
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Accéder
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SecretAccess;


