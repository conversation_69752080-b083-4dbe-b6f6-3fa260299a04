[build]
  command = "npm run build"
  publish = "dist"

# Force HTTPS redirects
[[redirects]]
  from = "http://laboutiquedeminnieci.com/*"
  to = "https://laboutiquedeminnieci.com/:splat"
  status = 301
  force = true

[[redirects]]
  from = "http://www.laboutiquedeminnieci.com/*"
  to = "https://laboutiquedeminnieci.com/:splat"
  status = 301
  force = true

[[redirects]]
  from = "/admin"
  to = "/"
  status = 301

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # ONLY add HSTS if HTTPS is working properly
    # Strict-Transport-Security = "max-age=31536000; includeSubDomains"
    
    # Content Security Policy (adjust as needed)
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;"
