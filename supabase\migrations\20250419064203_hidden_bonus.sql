/*
  # Set up admin authentication

  1. Security
    - Enable email authentication
    - Create initial admin user
    - Set up secure policies
*/

-- Enable email auth
ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

-- Insert initial admin user
INSERT INTO auth.users (
  instance_id,
  id,
  aud,
  role,
  email,
  encrypted_password,
  email_confirmed_at,
  recovery_sent_at,
  last_sign_in_at,
  raw_app_meta_data,
  raw_user_meta_data,
  created_at,
  updated_at,
  confirmation_token,
  email_change,
  email_change_token_new,
  recovery_token
) VALUES (
  '00000000-0000-0000-0000-000000000000',
  gen_random_uuid(),
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('Fraisemelody02032245', gen_salt('bf')),
  current_timestamp,
  current_timestamp,
  current_timestamp,
  '{"provider": "email", "providers": ["email"]}',
  '{"role": "admin"}',
  current_timestamp,
  current_timestamp,
  '',
  '',
  '',
  ''
);