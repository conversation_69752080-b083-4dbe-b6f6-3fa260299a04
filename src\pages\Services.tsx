import React from 'react';
import ServiceDetails from '../components/services/ServiceDetails';

const Services = () => {
  const services = [
    {
      title: "Événementiel Enfant",
      description: "Transformez les événements de vos enfants en souvenirs magiques avec nos décorations thématiques inspirées de l'univers Disney. Nous concevons des décors féeriques pour anniversaires, baptêmes et autres célébrations.",
      features: [
        "Décorations thématiques personnalisées",
        "Tables d'anniversaire magiques",
        "Arches de ballons colorés",
        "Backdrops pour photos souvenirs",
        "Sweet tables et candy bars"
      ],
      image: "/images/services/categories/enfant.jpg"
    },
    {
      title: "Événementiel Adulte",
      description: "Célébrez vos moments importants avec élégance et originalité. Nos décorations pour événements adultes allient sophistication et touches ludiques inspirées de l'univers Disney pour créer une ambiance unique.",
      features: [
        "Décorations élégantes et stylisées",
        "Centres de table sophistiqués",
        "Mise en scène thématique",
        "Éclairage d'ambiance",
        "Accessoires personnalisés"
      ],
      image: "/images/services/categories/adulte.jpg"
    },
    {
      title: "Arbre de Noël",
      description: "Créez une ambiance festive et magique pour vos célébrations de fin d'année. Nos décorations de Noël apportent chaleur et émerveillement à vos événements professionnels ou familiaux.",
      features: [
        "Arbres de Noël décorés sur mesure",
        "Illuminations festives",
        "Décors thématiques de Noël",
        "Accessoires et figurines de fêtes",
        "Ambiance chaleureuse et féerique"
      ],
      image: "/images/services/categories/noel.jpg"
    },
    {
      title: "Châteaux & Structures Gonflables",
      description: "Offrez des moments de divertissement inoubliables avec nos châteaux gonflables, bulles transparentes et trampolines. Des installations sécurisées et amusantes pour le plaisir des enfants.",
      features: [
        "Châteaux gonflables colorés et sécurisés",
        "Bulles transparentes avec ballons",
        "Trampolines pour sauter et s'amuser",
        "Installations adaptées à différents âges",
        "Montage et démontage professionnels"
      ],
      image: "/images/services/categories/chateau.jpg"
    },
    {
      title: "Mascottes & Animations",
      description: "Ajoutez une touche magique à votre événement avec nos mascottes et personnages. Du Père Noël aux nounours géants, nos animations raviront petits et grands.",
      features: [
        "Père Noël pour événements de fin d'année",
        "Mascottes de personnages populaires",
        "Nounours géants pour photos et câlins",
        "Animations interactives avec les invités",
        "Costumes de qualité professionnelle"
      ],
      image: "/images/services/categories/mascotte.jpg"
    },
    {
      title: "Stands Gourmands & Ateliers",
      description: "Régalez vos invités avec nos stands gourmands et offrez des activités créatives avec nos ateliers. Une combinaison parfaite de plaisirs gustatifs et d'activités ludiques.",
      features: [
        "Machines à barbe à papa professionnelles",
        "Stands de popcorn frais et crêpes",
        "Ateliers de coloriage pour enfants",
        "Maquillage festif et artistique",
        "Bar à cocktails et services traiteur"
      ],
      image: "/images/services/categories/stands.jpg"
    }
  ];

  return (
    <div className="pt-20">
      <div className="bg-primary-100 py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-display text-primary-700 mb-4">Nos Services</h1>
          <p className="text-lg text-secondary-700 max-w-2xl mx-auto">
            Découvrez notre gamme complète de services de décoration événementielle pour créer des moments magiques.
          </p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        <div className="space-y-20">
          {services.map((service, index) => (
            <ServiceDetails
              key={index}
              title={service.title}
              description={service.description}
              features={service.features}
              image={service.image}
              reverse={index % 2 !== 0}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Services;