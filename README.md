# La Boutique de Minnie - Site Web

Site web pour La Boutique de Minnie, spécialiste en décoration événementielle.

## Comment ajouter des images au site

Pour ajouter des images au site, suivez ces étapes simples:

### 1. Structure des dossiers d'images

Les images sont organisées dans le dossier `public/images/` selon la structure suivante:

```
public/images/
├── about/
│   ├── history/
│   ├── team/
│   └── values/
├── common/
│   ├── backgrounds/
│   ├── icons/
│   └── logo/
├── home/
│   ├── contact/
│   ├── gallery/
│   ├── hero/
│   ├── services/
│   └── testimonials/
├── services/
│   ├── categories/
│   └── products/
└── contact/
    ├── form/
    └── map/
```

### 2. Ajouter une nouvelle image

1. **Choisissez le bon dossier**: Déterminez dans quelle section du site l'image sera utilisée et placez-la dans le dossier correspondant.

2. **Nommez correctement l'image**: Le nom de l'image doit correspondre au nom du dossier qui la contient. Par exemple:
   - Pour une image dans `home/gallery/`, nommez-la `gallery1.jpg`, `gallery2.jpg`, etc.
   - Pour une image dans `services/categories/`, nommez-la `categories1.jpg`, `categories2.jpg`, etc.

3. **Formats d'image acceptés**: Utilisez uniquement des formats PNG ou JPG pour les images.

4. **Taille recommandée**: Pour de meilleures performances, optimisez vos images:
   - Résolution maximale: 1920x1080 pixels
   - Taille de fichier: moins de 500 KB si possible

### 3. Publier les modifications

Une fois que vous avez ajouté vos images:

1. Créez une nouvelle branche: `git checkout -b ajout-nouvelles-images`
2. Ajoutez vos images: `git add public/images/`
3. Validez vos modifications: `git commit -m "Ajout de nouvelles images"`
4. Poussez vers GitHub: `git push origin ajout-nouvelles-images`
5. Créez une Pull Request sur GitHub
6. Une fois la Pull Request fusionnée, le site sera automatiquement mis à jour avec vos nouvelles images

### Exemple

Pour ajouter une nouvelle image à la galerie de la page d'accueil:

1. Placez votre image dans le dossier `public/images/home/<USER>/`
2. Nommez-la `gallery7.jpg` (ou le prochain numéro disponible)
3. Suivez les étapes de publication ci-dessus

Le site détectera automatiquement la nouvelle image et l'affichera dans la section "Nouvelles Réalisations" de la galerie.

## Développement local

Pour exécuter le site en local:

```bash
# Installer les dépendances
npm install

# Démarrer le serveur de développement
npm run dev

# Ou avec le serveur personnalisé pour tester l'API d'images
npm run dev:server
```

## Déploiement

Le site peut être déployé sur plusieurs plateformes:

### GitHub Pages

```bash
# Construire le site et préparer pour GitHub Pages
npm run deploy:github

# Puis pousser le dossier dist vers la branche gh-pages
git add dist -f
git commit -m "Déploiement sur GitHub Pages"
git subtree push --prefix dist origin gh-pages
```

### Netlify

```bash
# Construire le site et préparer pour Netlify
npm run deploy:netlify

# Puis déployer via l'interface Netlify ou la CLI
npx netlify deploy --prod
```

### Vercel

```bash
# Construire le site et préparer pour Vercel
npm run deploy:vercel

# Puis déployer via l'interface Vercel ou la CLI
npx vercel --prod
```

## Optimisation des performances

Le site utilise plusieurs techniques d'optimisation:

1. **Lazy loading des images**: Toutes les images sont chargées de manière différée pour améliorer les performances.
2. **Optimisation des images**: Les images sont optimisées pour réduire leur taille.
3. **SEO**: Le site inclut des balises meta et des données structurées pour améliorer le référencement.
4. **Sitemap**: Un sitemap.xml est généré pour aider les moteurs de recherche à indexer le site.
