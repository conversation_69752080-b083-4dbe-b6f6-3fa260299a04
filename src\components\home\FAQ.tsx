import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface FAQItemProps {
  question: string;
  answer: string;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b border-secondary-200 py-4">
      <button
        type="button"
        className="flex justify-between items-center w-full text-left font-medium text-secondary-800 hover:text-primary-600 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
      >
        <span>{question}</span>
        <span className="ml-4 flex-shrink-0">
          {isOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
        </span>
      </button>
      {isOpen && (
        <div className="mt-2 text-secondary-600">
          <p>{answer}</p>
        </div>
      )}
    </div>
  );
};

const FAQ = () => {
  const faqs = [
    {
      question: "Quels types d'événements pouvez-vous décorer ?",
      answer: "Nous décorons tous types d'événements : anniversaires enfants et adultes, baptêmes, mariages, événements professionnels, arbres de Noël, et bien plus encore. Notre équipe s'adapte à vos besoins spécifiques."
    },
    {
      question: "Proposez-vous la location de châteaux gonflables et structures de jeux ?",
      answer: "Oui, nous proposons la location de châteaux gonflables, bulles transparentes et trampolines pour vos événements. Nos structures sont sécurisées et adaptées à différents âges."
    },
    {
      question: "Comment réserver vos services pour un événement ?",
      answer: "Pour réserver nos services, contactez-nous par téléphone au 057 414 46 73, par WhatsApp au +225 05 74 14 46 73, ou via notre formulaire de contact. Nous vous répondrons dans les plus brefs délais pour discuter de votre projet."
    },
    {
      question: "Où êtes-vous situés et dans quelles zones intervenez-vous ?",
      answer: "Notre siège est situé à Riviera - Palmeraie. Nous intervenons dans toute la région d'Abidjan et ses environs. Pour des événements plus éloignés, n'hésitez pas à nous contacter pour discuter des possibilités."
    },
    {
      question: "Proposez-vous des stands gourmands pour les événements ?",
      answer: "Oui, nous proposons des stands gourmands avec barbe à papa, popcorn, crêpes et autres délices. Nous pouvons également organiser un service de bar à cocktails et traiteur pour vos réceptions."
    },
    {
      question: "Peut-on réserver des mascottes ou personnages pour animer un événement ?",
      answer: "Absolument ! Nous proposons des mascottes, des personnages comme le Père Noël et des nounours géants pour animer vos événements et créer des moments magiques pour petits et grands."
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-display text-primary-700 mb-4">Questions Fréquentes</h2>
          <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
            Vous avez des questions sur nos services ? Consultez notre FAQ ci-dessous ou contactez-nous directement.
          </p>
        </div>
        
        <div className="max-w-3xl mx-auto">
          {faqs.map((faq, index) => (
            <FAQItem 
              key={index}
              question={faq.question}
              answer={faq.answer}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQ;
