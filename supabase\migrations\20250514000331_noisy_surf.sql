/*
  # Création des tables pour le back-office

  1. Tables
    - users : table pour stocker les informations des utilisateurs
    - invoices : table pour stocker les factures
    - settings : table pour les paramètres du site
    
  2. Sécurité
    - Activation de RLS sur toutes les tables
    - Politiques pour l'accès authentifié
*/

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255),
  phone VARCHAR(50),
  role VARCHAR(50) DEFAULT 'user',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Table des factures
CREATE TABLE IF NOT EXISTS invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_name VARCHAR(255) NOT NULL,
  client_email VARCHAR(255),
  client_phone VARCHAR(50),
  event_type VARCHAR(100),
  event_date DATE,
  total_amount INTEGER DEFAULT 0,
  status VARCHAR(50) DEFAULT 'pending',
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Table des paramètres
CREATE TABLE IF NOT EXISTS settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  key VARCHAR(255) UNIQUE NOT NULL,
  value TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Activation de RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;

-- Politiques pour users
CREATE POLICY "Les utilisateurs authentifiés peuvent lire leurs propres données"
  ON users FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Les administrateurs peuvent tout faire"
  ON users FOR ALL
  USING (auth.jwt() ->> 'role' = 'admin');

-- Politiques pour invoices
CREATE POLICY "Les utilisateurs authentifiés peuvent voir leurs factures"
  ON invoices FOR SELECT
  USING (client_email = (SELECT email FROM auth.users WHERE id = auth.uid()));

CREATE POLICY "Les administrateurs peuvent gérer toutes les factures"
  ON invoices FOR ALL
  USING (auth.jwt() ->> 'role' = 'admin');

-- Politiques pour settings
CREATE POLICY "Seuls les administrateurs peuvent gérer les paramètres"
  ON settings FOR ALL
  USING (auth.jwt() ->> 'role' = 'admin');