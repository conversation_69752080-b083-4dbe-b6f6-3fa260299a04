import React from 'react';

interface LinkProps {
  to: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export const Link: React.FC<LinkProps> = ({ to, children, className = '', onClick }) => {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();

    if (onClick) {
      onClick();
    }

    // Handle navigation
    if (to === '/') {
      // Pour la page d'accueil, faire défiler vers le haut et naviguer
      window.scrollTo({ top: 0, behavior: 'smooth' });
      // @ts-ignore
      if (window.navigateTo) {
        // @ts-ignore
        window.navigateTo(to);
      }
    } else {
      // Pour les autres pages, utiliser la fonction de navigation globale
      // @ts-ignore
      if (window.navigateTo) {
        // @ts-ignore
        window.navigateTo(to);
      } else {
        // Fallback si la fonction n'est pas disponible
        window.location.href = to;
      }
    }
  };

  return (
    <a href={to} className={className} onClick={handleClick}>
      {children}
    </a>
  );
};