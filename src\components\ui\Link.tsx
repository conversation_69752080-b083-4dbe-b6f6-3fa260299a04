import React from 'react';
import { useNavigate } from 'react-router-dom';

interface LinkProps {
  to: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export const Link: React.FC<LinkProps> = ({ to, children, className = '', onClick }) => {
  const navigate = useNavigate();
  
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();

    if (onClick) {
      onClick();
    }

    // Handle navigation using React Router
    if (to === '/') {
      // Pour la page d'accueil, faire défiler vers le haut et naviguer
      window.scrollTo({ top: 0, behavior: 'smooth' });
      navigate(to);
    } else {
      // Pour les autres pages, utiliser React Router navigate
      navigate(to);
    }
  };

  return (
    <a href={to} className={className} onClick={handleClick}>
      {children}
    </a>
  );
};