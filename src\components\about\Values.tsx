import React from 'react';
import Card from '../ui/Card';
import { Sparkles, Heart, Gem, Clock } from 'lucide-react';

interface ValueCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const ValueCard: React.FC<ValueCardProps> = ({ icon, title, description }) => {
  return (
    <Card className="text-center">
      <div className="inline-flex items-center justify-center p-3 bg-primary-100 rounded-full mb-4 text-primary-500">
        {icon}
      </div>
      <h3 className="text-xl font-bold text-secondary-800 mb-3">{title}</h3>
      <p className="text-secondary-600">{description}</p>
    </Card>
  );
};

const Values = () => {
  const values = [
    {
      icon: <Sparkles size={28} />,
      title: "Créativité",
      description: "Nous créons des designs uniques et personnalisés pour chaque client, en fonction de ses goûts et de ses besoins."
    },
    {
      icon: <Heart size={28} />,
      title: "Passion",
      description: "Notre passion pour la décoration nous pousse à nous surpasser à chaque projet pour créer des ambiances magiques."
    },
    {
      icon: <Gem size={28} />,
      title: "Qualité",
      description: "Nous utilisons des matériaux de qualité et portons une attention particulière aux moindres détails."
    },
    {
      icon: <Clock size={28} />,
      title: "Ponctualité",
      description: "Nous respectons scrupuleusement les délais pour que votre événement se déroule sans stress."
    }
  ];

  return (
    <section className="py-16 bg-primary-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-display text-primary-700 mb-4">Nos Valeurs</h2>
          <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
            Découvrez les valeurs qui guident notre travail et notre engagement envers nos clients.
          </p>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {values.map((value, index) => (
            <ValueCard 
              key={index}
              icon={value.icon}
              title={value.title}
              description={value.description}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Values;