import jsPDF from 'jspdf';

export interface QuoteItemData {
  name: string;
  quantity: number;
}

export interface DevisData {
  name: string;
  email: string;
  phone: string;
  eventType: string;
  eventDate?: string; // Changed from 'date' and made optional as it might not always be present
  location?: string;
  guestsCount?: number;
  message: string;
  rentalItems?: QuoteItemData[] | null;
  // 'date' field is removed as eventDate is more specific. If a general submission date is needed, it can be handled differently.
}

export const generateDevisPDF = (data: DevisData): Blob => {
  const doc = new jsPDF();
  
  // Configuration des couleurs
  const primaryColor = [236, 72, 153]; // Rose primary
  const secondaryColor = [75, 85, 99]; // Gris secondary
  
  // En-tête avec logo et informations entreprise
  doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
  doc.rect(0, 0, 210, 40, 'F');
  
  // Titre de l'entreprise
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(24);
  doc.setFont('helvetica', 'bold');
  doc.text('La Boutique de Minnie', 20, 25);
  
  // Sous-titre
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  doc.text('Décoration d\'événements & Location d\'équipements', 20, 32);
  
  // Informations de contact entreprise
  doc.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
  doc.setFontSize(10);
  doc.text('Tél: 057 414 46 73 | WhatsApp: +225 05 74 14 46 73', 20, 50);
  doc.text('Riviera - Palmeraie, Abidjan', 20, 56);
  
  // Titre du document
  doc.setFontSize(18);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
  doc.text('DEMANDE DE DEVIS', 20, 75);
  
  // Date
  const currentDate = data.eventDate ? new Date(data.eventDate).toLocaleDateString('fr-FR') : new Date().toLocaleDateString('fr-FR');
  doc.setFontSize(10);
  doc.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
  doc.text(`Date: ${currentDate}`, 150, 75);
  
  // Informations client
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
  doc.text('INFORMATIONS CLIENT', 20, 95);
  
  // Ligne de séparation
  doc.setDrawColor(primaryColor[0], primaryColor[1], primaryColor[2]);
  doc.setLineWidth(0.5);
  doc.line(20, 98, 190, 98);
  
  // Détails client
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  let yPos = 110;
  
  doc.setFont('helvetica', 'bold');
  doc.text('Nom:', 20, yPos);
  doc.setFont('helvetica', 'normal');
  doc.text(data.name, 45, yPos);
  yPos += 8;
  
  doc.setFont('helvetica', 'bold');
  doc.text('Email:', 20, yPos);
  doc.setFont('helvetica', 'normal');
  doc.text(data.email, 45, yPos);
  yPos += 8;
  
  doc.setFont('helvetica', 'bold');
  doc.text('Téléphone:', 20, yPos);
  doc.setFont('helvetica', 'normal');
  doc.text(data.phone || 'Non renseigné', 45, yPos);
  yPos += 8;
  
  doc.setFont('helvetica', 'bold');
  doc.text('Type d\'événement:', 20, yPos);
  doc.setFont('helvetica', 'normal');
  doc.text(getEventTypeLabel(data.eventType), 60, yPos);
  yPos += 8;

  if (data.eventDate) {
    doc.setFont('helvetica', 'bold');
    doc.text('Date de l\'événement:', 20, yPos);
    doc.setFont('helvetica', 'normal');
    doc.text(new Date(data.eventDate).toLocaleDateString('fr-FR'), 65, yPos);
    yPos += 8;
  }

  if (data.location) {
    doc.setFont('helvetica', 'bold');
    doc.text('Lieu:', 20, yPos);
    doc.setFont('helvetica', 'normal');
    doc.text(data.location, 45, yPos);
    yPos += 8;
  }

  if (data.guestsCount) {
    doc.setFont('helvetica', 'bold');
    doc.text('Nombre d\'invités:', 20, yPos);
    doc.setFont('helvetica', 'normal');
    doc.text(data.guestsCount.toString(), 60, yPos);
    yPos += 8;
  }
  yPos += 7; // Extra space before next section
  
  // Description du projet
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('DESCRIPTION DU PROJET', 20, yPos);
  
  // Ligne de séparation
  doc.line(20, yPos + 3, 190, yPos + 3);
  yPos += 15;
  
  // Message client
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  const splitMessage = doc.splitTextToSize(data.message || 'Aucun message.', 170);
  doc.text(splitMessage, 20, yPos);
  yPos += splitMessage.length * 6 + 10; // Adjusted spacing

  // Display Rental Items
  if (data.rentalItems && data.rentalItems.length > 0) {
    yPos += 10;
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
    doc.text('MATÉRIEL DE LOCATION SOUHAITÉ', 20, yPos);
    doc.setDrawColor(primaryColor[0], primaryColor[1], primaryColor[2]);
    doc.setLineWidth(0.5);
    doc.line(20, yPos + 3, 190, yPos + 3);
    yPos += 15;

    doc.setFontSize(10);
    doc.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
    doc.setFont('helvetica', 'normal');

    data.rentalItems.forEach(item => {
      if (yPos > 250) { // Check for page break
        doc.addPage();
        yPos = 20; // Reset Y position for new page
      }
      doc.text(`• ${item.name} (Quantité: ${item.quantity})`, 25, yPos);
      yPos += 6;
    });
    yPos += 10; // Add some space after the list
  }

  
  // Services proposés
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
  doc.text('NOS SERVICES', 20, yPos);
  
  doc.line(20, yPos + 3, 190, yPos + 3);
  yPos += 15;
  
  // Liste des services
  doc.setFontSize(10);
  doc.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
  doc.setFont('helvetica', 'normal');
  
  const services = [
    '• Décoration complète d\'événements',
    '• Location de châteaux gonflables et structures de jeux',
    '• Animation avec mascottes et personnages',
    '• Stands gourmands (barbe à papa, popcorn, crêpes)',
    '• Service traiteur et bar à cocktails',
    '• Bulles transparentes et trampolines'
  ];
  
  services.forEach(service => {
    doc.text(service, 20, yPos);
    yPos += 6;
  });
  
  // Pied de page
  yPos = 260;
  doc.setFillColor(240, 240, 240);
  doc.rect(0, yPos, 210, 30, 'F');
  
  doc.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
  doc.setFontSize(10);
  doc.setFont('helvetica', 'italic');
  doc.text('Ce devis est valable 30 jours. Contactez-nous pour finaliser votre commande.', 20, yPos + 10);
  doc.text('La Boutique de Minnie - Votre partenaire pour des événements magiques !', 20, yPos + 20);
  
  // Retourner le PDF comme Blob
  return doc.output('blob');
};

const getEventTypeLabel = (eventType: string): string => {
  const eventTypes: { [key: string]: string } = {
    'enfant': 'Événement pour enfant',
    'adulte': 'Événement pour adulte',
    'mariage': 'Mariage',
    'conference': 'Conférence/Événement professionnel',
    'autre': 'Autre'
  };
  
  return eventTypes[eventType] || 'Non spécifié';
};

export const downloadPDF = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export const sendPDFToWhatsApp = (data: DevisData) => {
  // Générer le PDF
  const pdfBlob = generateDevisPDF(data);
  
  // Créer un message WhatsApp avec instructions
  const whatsappMessage = `🎉 *Nouvelle demande de devis - La Boutique de Minnie*\n\n` +
    `👤 *Client:* ${data.name}\n` +
    `📧 *Email:* ${data.email}\n` +
    `📱 *Téléphone:* ${data.phone || 'Non renseigné'}\n` +
    `🎊 *Type d'événement:* ${getEventTypeLabel(data.eventType)}\n\n` +
    `📄 *Un fichier PDF détaillé avec toutes les informations a été généré.*\n` +
    `Veuillez le télécharger et l'envoyer en pièce jointe dans ce chat WhatsApp.\n\n` +
    `✨ Merci de me contacter pour établir un devis personnalisé !`;
  
  // Télécharger le PDF
  const filename = `devis-${data.name.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.pdf`;
  downloadPDF(pdfBlob, filename);
  
  // Ouvrir WhatsApp avec le message
  const encodedMessage = encodeURIComponent(whatsappMessage);
  const whatsappUrl = `https://wa.me/2250574144673?text=${encodedMessage}`;
  
  // Délai pour permettre le téléchargement du PDF avant d'ouvrir WhatsApp
  setTimeout(() => {
    window.open(whatsappUrl, '_blank');
  }, 1000);
};