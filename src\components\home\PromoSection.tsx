import React from 'react';
import { Phone, Calendar } from 'lucide-react';
import { Link } from '../ui/Link';

const PromoSection = () => {
  return (
    <section className="py-16 bg-gradient-to-b from-primary-50 to-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-display text-primary-700 mb-4">Confiez-nous vos événements</h2>
          <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
            L'assurance d'un événement réussi avec notre gamme complète de services et d'animations.
          </p>
        </div>

        <div className="flex flex-col lg:flex-row items-center gap-10">
          <div className="lg:w-1/2">
            <div className="relative rounded-2xl overflow-hidden shadow-xl">
              <img
                src="/images/home/<USER>/promo-flyer.jpg"
                alt="Nos services - Châteaux gonflables, bulles, arbre de noël, mascottes et plus"
                className="w-full h-auto rounded-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary-500/20 to-transparent"></div>
            </div>
          </div>

          <div className="lg:w-1/2">
            <h3 className="text-2xl md:text-3xl font-display text-primary-700 mb-6">Nos prestations complètes</h3>
            
            <ul className="space-y-4 mb-8">
              <li className="flex items-start">
                <span className="bg-primary-100 text-primary-600 p-1 rounded-full mr-3 mt-0.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </span>
                <span className="text-secondary-700">Arbre de noël</span>
              </li>
              <li className="flex items-start">
                <span className="bg-primary-100 text-primary-600 p-1 rounded-full mr-3 mt-0.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </span>
                <span className="text-secondary-700">Événementiel enfants et adultes</span>
              </li>
              <li className="flex items-start">
                <span className="bg-primary-100 text-primary-600 p-1 rounded-full mr-3 mt-0.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </span>
                <span className="text-secondary-700">Décoration Anniversaire, Baptême...</span>
              </li>
              <li className="flex items-start">
                <span className="bg-primary-100 text-primary-600 p-1 rounded-full mr-3 mt-0.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </span>
                <span className="text-secondary-700">Châteaux à bulles et à trampolines</span>
              </li>
              <li className="flex items-start">
                <span className="bg-primary-100 text-primary-600 p-1 rounded-full mr-3 mt-0.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </span>
                <span className="text-secondary-700">Père noël, Mascottes et Nounours Géants</span>
              </li>
              <li className="flex items-start">
                <span className="bg-primary-100 text-primary-600 p-1 rounded-full mr-3 mt-0.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </span>
                <span className="text-secondary-700">Sonorisation et Clowns</span>
              </li>
              <li className="flex items-start">
                <span className="bg-primary-100 text-primary-600 p-1 rounded-full mr-3 mt-0.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </span>
                <span className="text-secondary-700">Stands gourmands : Barbe à papa, Popcorn, crêpes...</span>
              </li>
              <li className="flex items-start">
                <span className="bg-primary-100 text-primary-600 p-1 rounded-full mr-3 mt-0.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </span>
                <span className="text-secondary-700">Ateliers de coloriage et maquillage enfants</span>
              </li>
              <li className="flex items-start">
                <span className="bg-primary-100 text-primary-600 p-1 rounded-full mr-3 mt-0.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </span>
                <span className="text-secondary-700">Bar à cocktails, traiteurs, et bien plus encore...</span>
              </li>
            </ul>

            <div className="flex flex-col sm:flex-row gap-4">
              <Link to="/contact">
                <button type="button" className="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full transition-colors font-medium inline-flex items-center">
                  <Phone className="mr-2" size={20} />
                  Contactez-nous au 057 414 46 73
                </button>
              </Link>
              <Link to="/services">
                <button type="button" className="bg-secondary-100 hover:bg-secondary-200 text-secondary-700 px-6 py-3 rounded-full transition-colors font-medium inline-flex items-center">
                  <Calendar className="mr-2" size={20} />
                  Planifier un événement
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PromoSection;
