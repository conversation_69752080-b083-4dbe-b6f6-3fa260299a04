import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hoverEffect?: boolean;
}

const Card: React.FC<CardProps> = ({ 
  children, 
  className = '', 
  hoverEffect = false
}) => {
  return (
    <div className={`
      bg-white rounded-2xl overflow-hidden shadow-md p-6
      ${hoverEffect ? 'transition-transform duration-300 hover:-translate-y-2 hover:shadow-lg' : ''}
      ${className}
    `}>
      {children}
    </div>
  );
};

export default Card;