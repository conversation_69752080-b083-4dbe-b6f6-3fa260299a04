import React from 'react';
import { Facebook, Mail, Phone } from 'lucide-react';
import { Link } from '../ui/Link';
import { ADMIN_TOKEN } from '../../lib/adminAccess';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-secondary-900 text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-2xl font-display text-primary-300 mb-4">La Boutique de Minnie</h3>
            <p className="mb-4 text-secondary-300">
              L'assurance d'un événement réussi ! Créateur d'ambiances magiques pour tous vos événements spéciaux.
            </p>
            <div className="flex space-x-4">
              <a
                href="https://www.facebook.com/laboutiquedeminnieci"
                target="_blank"
                rel="noopener noreferrer"
                className="text-secondary-300 hover:text-primary-400 transition-colors"
                aria-label="Facebook"
                title="Suivez-nous sur Facebook"
              >
                <Facebook size={24} />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-secondary-300 hover:text-primary-400 transition-colors"
                aria-label="Email"
                title="Envoyez-nous un email"
              >
                <Mail size={24} />
              </a>
              <a
                href="tel:+2250574144673"
                className="text-secondary-300 hover:text-primary-400 transition-colors"
                aria-label="Téléphone"
                title="Appelez-nous"
              >
                <Phone size={24} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-bold mb-4">Liens Rapides</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-secondary-300 hover:text-primary-400 transition-colors">
                  Accueil
                </Link>
              </li>
              <li>
                <Link to="/services" className="text-secondary-300 hover:text-primary-400 transition-colors">
                  Nos Services
                </Link>
              </li>
              <li>
                <Link to="/gallery" className="text-secondary-300 hover:text-primary-400 transition-colors">
                  Galerie
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-secondary-300 hover:text-primary-400 transition-colors">
                  À Propos
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-secondary-300 hover:text-primary-400 transition-colors">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-xl font-bold mb-4">Contact</h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <Phone size={20} className="mr-2 mt-1 text-primary-400" />
                <div>
                  <p>Téléphone:</p>
                  <a href="tel:+2250574144673" className="text-secondary-300 hover:text-primary-400 transition-colors">
                    057 414 46 73
                  </a>
                  <p className="text-sm text-secondary-400">(WhatsApp: +225 05 74 14 46 73)</p>
                </div>
              </li>
              <li className="flex items-start">
                <Mail size={20} className="mr-2 mt-1 text-primary-400" />
                <div>
                  <p>Email:</p>
                  <a href="mailto:<EMAIL>" className="text-secondary-300 hover:text-primary-400 transition-colors">
                    <EMAIL>
                  </a>
                </div>
              </li>
              <li className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 mt-1 text-primary-400">
                  <path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 18a8 8 0 1 1 8-8 8 8 0 0 1-8 8z"/>
                  <path d="M12 6v6l4 2"/>
                </svg>
                <div>
                  <p>Siège:</p>
                  <p className="text-secondary-300">Riviera - Palmeraie</p>
                </div>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-secondary-700 mt-8 pt-6 text-center text-secondary-400">
          <p>&copy; {currentYear} La Boutique de Minnie. Tous droits réservés.</p>
          <Link to={`/secure-admin/${ADMIN_TOKEN}`} className="text-primary-300 text-sm hover:text-primary-200 transition-colors mt-2 inline-block">
            Administration
          </Link>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

