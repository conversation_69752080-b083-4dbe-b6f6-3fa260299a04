/**
 * Utility for dynamically loading images from the public folder
 */

export async function getImagesFromFolder(folderPath: string): Promise<string[]> {
  try {
    const response = await fetch(`/api/images?folder=${encodeURIComponent(folderPath)}`);
    
    // Log response details for debugging
    console.debug('Image loader response:', {
      status: response.status,
      contentType: response.headers.get('Content-Type'),
      url: response.url
    });

    // Check if the response is JSON before attempting to parse
    const contentType = response.headers.get('Content-Type');
    if (!contentType?.includes('application/json')) {
      console.error(`Expected JSON response but got ${contentType}`);
      return [];
    }

    // Parse JSON response
    const data = await response.json();
    
    // Validate response structure
    if (!data || !Array.isArray(data.images)) {
      console.error('Invalid response structure: missing images array');
      return [];
    }

    // Check if there's an error message in the response
    if (data.error) {
      console.warn('Server reported error:', data.error);
      return data.images; // Return empty array from server
    }
    
    return data.images;
  } catch (error) {
    // Log the full error for debugging
    console.error('Error loading images:', error);
    
    // Return empty array on error
    return [];
  }
}

export function isImageFile(filename: string): boolean {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
  return imageExtensions.includes(extension);
}

export function getFolderName(path: string): string {
  const parts = path.split('/');
  return parts[parts.length - 2] || '';
}