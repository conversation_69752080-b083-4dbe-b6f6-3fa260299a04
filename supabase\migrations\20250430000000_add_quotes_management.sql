-- Migration pour ajouter la gestion des devis
CREATE TABLE IF NOT EXISTS quotes (
  id SERIAL PRIMARY KEY,
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(50),
  event_type VARCHAR(100) NOT NULL,
  event_date DATE,
  location VARCHAR(255),
  guests_count INTEGER,
  message TEXT,
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'contacted', 'approved', 'rejected')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ajouter un trigger pour mettre à jour le timestamp
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_timestamp
BEFORE UPDATE ON quotes
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Politiques RLS pour sécuriser l'accès
ALTER TABLE quotes ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre aux admins de voir et modifier les devis
CREATE POLICY "Allow admin full access to quotes" ON quotes
    FOR ALL USING (auth.jwt() ->> 'email' = '<EMAIL>');

-- Politique pour permettre aux utilisateurs anonymes de créer des devis
CREATE POLICY "Allow anonymous to create quotes" ON quotes
    FOR INSERT WITH CHECK (true);