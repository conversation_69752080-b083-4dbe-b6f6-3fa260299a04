import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useParams, useNavigate } from 'react-router-dom';
import Layout from './components/layout/Layout';
import Home from './pages/Home';
import About from './pages/About';
import Services from './pages/Services';
import Gallery from './pages/Gallery';
import Contact from './pages/Contact';
import Admin from './pages/Admin';
import Devis from './pages/Devis';
import Location from './pages/Location';
import SecretAccess from './pages/SecretAccess';
import AdminFallback from './pages/AdminFallback';
import WhatsAppButton from './components/home/<USER>';
import { validateAdminToken } from './lib/adminAccess';

// Composant de protection
const ProtectedAdminRoute = ({ children }: { children: React.ReactNode }) => {
  const { token } = useParams();
  const navigate = useNavigate();
  
  React.useEffect(() => {
    if (!token || !validateAdminToken(token)) {
      navigate('/', { replace: true });
    }
  }, [token, navigate]);
  
  if (!token || !validateAdminToken(token)) {
    return null;
  }
  
  return <>{children}</>;
};

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout><Home /></Layout>} />
        <Route path="/about" element={<Layout><About /></Layout>} />
        <Route path="/services" element={<Layout><Services /></Layout>} />
        <Route path="/devis" element={<Layout><Devis /></Layout>} />
        <Route path="/location" element={<Layout><Location /></Layout>} />
        <Route path="/gallery" element={<Layout><Gallery /></Layout>} />
        <Route path="/contact" element={<Layout><Contact /></Layout>} />
        <Route path="/secret-access" element={<Layout><SecretAccess /></Layout>} />
        <Route path="/secure-admin/:token" element={<Layout><Admin /></Layout>} />
        <Route path="/admin-fallback" element={<AdminFallback />} />
        <Route path="/admin" element={<Navigate to="/" replace />} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
      <WhatsAppButton />
    </Router>
  );
}

export default App;




