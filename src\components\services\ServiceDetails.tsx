import React from 'react';
import { Check } from 'lucide-react';
import Button from '../ui/Button';
import { Link } from '../ui/Link';

interface ServiceDetailsProps {
  title: string;
  description: string;
  features: string[];
  image: string;
  reverse?: boolean;
}

const ServiceDetails: React.FC<ServiceDetailsProps> = ({
  title,
  description,
  features,
  image,
  reverse = false
}) => {
  return (
    <div className={`flex flex-col ${reverse ? 'md:flex-row-reverse' : 'md:flex-row'} items-center gap-12 py-12`}>
      <div className="md:w-1/2">
        <div className="relative">
          <img 
            src={image} 
            alt={title} 
            className="rounded-2xl shadow-lg w-full h-auto"
          />
          {/* Decorative elements */}
          <div className="absolute -bottom-5 -right-5 w-20 h-20 rounded-full bg-primary-200 -z-10"></div>
          {reverse && (
            <div className="absolute -top-5 -left-5 w-16 h-16 rounded-full bg-primary-300 -z-10"></div>
          )}
        </div>
      </div>
      
      <div className="md:w-1/2">
        <h3 className="text-2xl md:text-3xl font-display text-primary-700 mb-4">{title}</h3>
        <p className="text-secondary-700 mb-6">
          {description}
        </p>
        
        <ul className="space-y-3 mb-8">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start">
              <span className="bg-primary-100 text-primary-600 p-1 rounded-full mr-3 mt-0.5">
                <Check size={16} />
              </span>
              <span className="text-secondary-700">{feature}</span>
            </li>
          ))}
        </ul>
        
        <Link to="/contact">
          <Button>
            Demander un Devis
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default ServiceDetails;