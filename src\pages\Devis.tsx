import React, { useState } from 'react';
import { supabase } from '../lib/supabase';
import toast, { Toaster } from 'react-hot-toast';

const Devis = () => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    event_type: '',
    event_date: '',
    location: '',
    guests_count: '',
    message: '',
    rental_items: [] as { item: string; quantity: number }[] // Modifié pour stocker l'article et la quantité
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    const target = e.target as HTMLInputElement;

    if (name === 'rental_item_checkbox') {
      const itemName = target.value;
      const checked = target.checked;
      setFormData(prev => {
        const existingItem = prev.rental_items.find(ri => ri.item === itemName);
        if (checked) {
          if (!existingItem) {
            return { ...prev, rental_items: [...prev.rental_items, { item: itemName, quantity: 1 }] };
          }
        } else {
          return { ...prev, rental_items: prev.rental_items.filter(ri => ri.item !== itemName) };
        }
        return prev;
      });
    } else if (name.startsWith('rental_item_quantity_')) {
      const itemName = name.replace('rental_item_quantity_', '');
      const quantity = parseInt(value, 10);
      setFormData(prev => ({
        ...prev,
        rental_items: prev.rental_items.map(ri => 
          ri.item === itemName ? { ...ri, quantity: quantity > 0 ? quantity : 1 } : ri
        )
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const rentalOptions = [
    { id: 'ballons', label: 'Ballons' },
    { id: 'arches', label: 'Arches' },
    { id: 'guirlandes', label: 'Guirlandes' },
    { id: 'tables', label: 'Tables' },
    { id: 'chaises', label: 'Chaises' },
    { id: 'mange-debout', label: 'Mange-debout' },
    { id: 'assiettes', label: 'Assiettes' },
    { id: 'couverts', label: 'Couverts' },
    { id: 'verres', label: 'Verres' },
    { id: 'enceintes', label: 'Enceintes' },
    { id: 'micros', label: 'Micros' },
    { id: 'projecteurs', label: 'Projecteurs' },
    { id: 'structures-gonflables', label: 'Structures gonflables' },
    { id: 'photobooth', label: 'Photobooth' },
    // Vous pouvez ajouter d'autres éléments spécifiques ici
    { id: 'pack-thematique', label: 'Pack Thématique' },
    { id: 'pack-mobilier', label: 'Pack Mobilier' },
    { id: 'pack-vaisselle', label: 'Pack Vaisselle' },
    { id: 'pack-animation', label: 'Pack Animation' }
  ];

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { error } = await supabase
        .from('quotes')
        .insert([
          {
            name: formData.name,
            email: formData.email,
            phone: formData.phone,
            event_type: formData.event_type,
            event_date: formData.event_date || null,
            location: formData.location,
            guests_count: formData.guests_count ? parseInt(formData.guests_count) : null,
            message: formData.message,
            rental_items: formData.rental_items // Ajout pour les articles de location
          }
        ]);

      if (error) throw error;

      toast.success('Votre demande de devis a été envoyée avec succès!');
      setFormData({
        name: '',
        email: '',
        phone: '',
        event_type: '',
        event_date: '',
        location: '',
        guests_count: '',
        message: '',
        rental_items: []
      });
    } catch (error) {
      console.error('Erreur lors de l\'envoi du devis:', error);
      toast.error('Une erreur est survenue lors de l\'envoi de votre demande.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <Toaster position="top-right" />
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl md:text-5xl font-display text-primary-700 mb-8 text-center">
          Demander un Devis
        </h1>
        
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <p className="text-lg text-secondary-700 mb-8 text-center">
            Obtenez un devis personnalisé pour votre événement. Remplissez le formulaire ci-dessous et nous vous recontacterons rapidement.
          </p>
          
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="firstName" className="block text-sm font-medium text-secondary-700 mb-2">
                  Prénom *
                </label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  required
                  className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  value={formData.name}
                  onChange={handleChange}
                />
              </div>
              
              <div>
                <label htmlFor="lastName" className="block text-sm font-medium text-secondary-700 mb-2">
                  Nom *
                </label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  required
                  className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  value={formData.name}
                  onChange={handleChange}
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-secondary-700 mb-2">
                  Email *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  value={formData.email}
                  onChange={handleChange}
                />
              </div>
              
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-secondary-700 mb-2">
                  Téléphone
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  value={formData.phone}
                  onChange={handleChange}
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="eventType" className="block text-sm font-medium text-secondary-700 mb-2">
                Type d'événement *
              </label>
              <select
                id="eventType"
                name="eventType"
                required
                className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                value={formData.event_type}
                onChange={handleChange}
              >
                <option value="">Sélectionnez un type d'événement</option>
                <option value="anniversaire-enfant">Anniversaire enfant</option>
                <option value="anniversaire-adulte">Anniversaire adulte</option>
                <option value="bapteme">Baptême</option>
                <option value="communion">Communion</option>
                <option value="mariage">Mariage</option>
                <option value="baby-shower">Baby shower</option>
                <option value="autre">Autre</option>
              </select>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="eventDate" className="block text-sm font-medium text-secondary-700 mb-2">
                  Date de l'événement *
                </label>
                <input
                  type="date"
                  id="eventDate"
                  name="eventDate"
                  required
                  className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  value={formData.event_date}
                  onChange={handleChange}
                />
              </div>
              
              <div>
                <label htmlFor="guestCount" className="block text-sm font-medium text-secondary-700 mb-2">
                  Nombre d'invités
                </label>
                <input
                  type="number"
                  id="guestCount"
                  name="guestCount"
                  min="1"
                  className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  value={formData.guests_count}
                  onChange={handleChange}
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="location" className="block text-sm font-medium text-secondary-700 mb-2">
                Lieu de l'événement
              </label>
              <input
                type="text"
                id="location"
                name="location"
                placeholder="Ville, adresse ou lieu"
                className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                value={formData.location}
                onChange={handleChange}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-2">
                Matériel de location souhaité (optionnel)
              </label>
              <div className="space-y-2 mt-2 grid grid-cols-1 md:grid-cols-2 gap-x-6">
                {rentalOptions.map(option => (
                  <div key={option.id} className="flex items-center">
                    <input
                      id={option.id}
                      name="rental_item_checkbox" // Changé pour la gestion spécifique
                      type="checkbox"
                      value={option.label}
                      checked={formData.rental_items.some(ri => ri.item === option.label)}
                      onChange={handleChange}
                      className="h-4 w-4 text-primary-600 border-secondary-300 rounded focus:ring-primary-500"
                    />
                    <label htmlFor={option.id} className="ml-2 block text-sm text-secondary-700">
                      {option.label}
                    </label>
                    {formData.rental_items.some(ri => ri.item === option.label) && (
                      <input
                        type="number"
                        name={`rental_item_quantity_${option.label}`}
                        aria-label={`Quantité pour ${option.label}`}
                        min="1"
                        value={formData.rental_items.find(ri => ri.item === option.label)?.quantity || 1}
                        onChange={handleChange}
                        className="ml-2 w-16 px-2 py-1 border border-secondary-300 rounded-lg text-sm focus:ring-1 focus:ring-primary-500 focus:border-transparent"
                      />
                    )}
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-secondary-700 mb-2">
                Message / Détails supplémentaires
              </label>
              <textarea
                id="message"
                name="message"
                rows={5}
                placeholder="Décrivez votre projet, vos souhaits, votre budget approximatif..."
                className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                value={formData.message}
                onChange={handleChange}
              ></textarea>
            </div>
            
            <div className="text-center">
              <button
                type="submit"
                className="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3 rounded-full font-medium transition-colors text-lg"
                disabled={loading}
              >
                {loading ? "Envoi en cours..." : "Envoyer ma demande de devis"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Devis;

