import React from 'react';
import { Link } from 'react-router-dom';

const AdminFallback = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 p-4">
      <div className="bg-white shadow-md rounded-lg p-8 max-w-md w-full">
        <h1 className="text-2xl font-bold text-center mb-6">Administration de secours</h1>
        <p className="mb-4">
          Cette page est une interface d'administration simplifiée en cas de problème avec l'interface principale.
        </p>
        
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 rounded-md">
            <h2 className="font-semibold text-lg mb-2">Informations système</h2>
            <ul className="list-disc pl-5 space-y-1">
              <li>URL Supabase: {import.meta.env.VITE_SUPABASE_URL ? "Configurée" : "Non configurée"}</li>
              <li>Clé Supabase: {import.meta.env.VITE_SUPABASE_ANON_KEY ? "Configurée" : "Non configurée"}</li>
              <li>Version React: {React.version}</li>
            </ul>
          </div>
          
          <div className="flex justify-center">
            <Link 
              to="/"
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
            >
              Retour à l'accueil
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminFallback;