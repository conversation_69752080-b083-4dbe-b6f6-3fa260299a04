# Structure des images pour La Boutique de Minnie

Ce dossier contient toutes les images utilisées sur le site web de La Boutique de Minnie, organisées par page et par section.

## Structure des dossiers

### Images communes (`common/`)
- `logo/` : Logo et icônes de la marque
- `icons/` : Icônes utilisées sur plusieurs pages
- `backgrounds/` : Arrière-plans et textures réutilisables

### Page d'accueil (`home/`)
- `hero/` : Images pour la section d'en-tête/bannière
- `services/` : Images illustrant les services
- `gallery/` : Images pour la galerie de produits
- `testimonials/` : Photos des clients pour les témoignages
- `contact/` : Images pour la section contact

### Page À propos (`about/`)
- `team/` : Photos de l'équipe
- `history/` : Images illustrant l'histoire de l'entreprise
- `values/` : Images représentant les valeurs de l'entreprise

### Page Services (`services/`)
- `categories/` : Images des catégories de services
- `products/` : Images des produits individuels

### Page Contact (`contact/`)
- `map/` : Images liées à la carte ou à l'emplacement
- `form/` : Images décoratives pour le formulaire de contact

### Page Admin (`admin/`)
- `dashboard/` : Images pour le tableau de bord
- `icons/` : Icônes spécifiques à l'interface d'administration

## Recommandations pour les images

1. **Format** : Utilisez des formats optimisés pour le web (WebP de préférence, sinon JPG pour les photos, PNG pour les images avec transparence, SVG pour les icônes)
2. **Taille** : Optimisez les images pour réduire leur poids (max 200KB pour les grandes images)
3. **Dimensions** : Respectez les dimensions recommandées pour chaque emplacement
4. **Nommage** : Utilisez des noms descriptifs en minuscules, avec des tirets pour séparer les mots (ex: `hero-banner-principal.webp`)

## Utilisation dans le code

Pour utiliser ces images dans votre code React, importez-les comme ceci :

```jsx
// Pour les images statiques dans le dossier public
<img src="/images/home/<USER>/banner.webp" alt="Description" />
```

Ou si vous préférez importer les images dans vos composants :

```jsx
import bannerImage from '/public/images/home/<USER>/banner.webp';

// Puis dans votre JSX
<img src={bannerImage} alt="Description" />
```
