-- Migration pour ajouter la gestion de contenu au back office
-- Tables pour permettre la modification des titres et images du site

-- Table pour le contenu des pages (titres, textes, etc.)
CREATE TABLE IF NOT EXISTS site_content (
  id SERIAL PRIMARY KEY,
  page_name VARCHAR(50) NOT NULL,
  section_name VARCHAR(50) NOT NULL,
  content_type VARCHAR(20) NOT NULL CHECK (content_type IN ('title', 'text', 'image', 'button')),
  content_key VARCHAR(100) NOT NULL,
  content_value TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour la gestion des médias (images, vidéos)
CREATE TABLE IF NOT EXISTS media_library (
  id SERIAL PRIMARY KEY,
  filename VARCHAR(255) NOT NULL,
  original_name VARCHAR(255) NOT NULL,
  file_path TEXT NOT NULL,
  file_type VARCHAR(50) NOT NULL,
  file_size INTEGER,
  alt_text TEXT,
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_site_content_page_section ON site_content(page_name, section_name);
CREATE INDEX IF NOT EXISTS idx_site_content_key ON site_content(content_key);
CREATE INDEX IF NOT EXISTS idx_media_library_filename ON media_library(filename);

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger pour mettre à jour automatiquement updated_at
CREATE TRIGGER update_site_content_updated_at BEFORE UPDATE ON site_content
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insertion du contenu par défaut de la page d'accueil
INSERT INTO site_content (page_name, section_name, content_type, content_key, content_value) VALUES
-- Hero Section
('home', 'hero', 'title', 'main_title', 'Créez des Moments Magiques'),
('home', 'hero', 'text', 'main_description', 'La Boutique de Minnie transforme vos événements en expériences inoubliables avec des décorations féeriques et des animations pour tous.'),
('home', 'hero', 'text', 'tagline', 'L''assurance d''un événement réussi !'),
('home', 'hero', 'image', 'hero_image', '/images/home/<USER>/hero.png'),
('home', 'hero', 'button', 'primary_button_text', 'Demander un devis'),
('home', 'hero', 'button', 'primary_button_link', '/devis'),
('home', 'hero', 'button', 'secondary_button_text', 'Location d''articles'),
('home', 'hero', 'button', 'secondary_button_link', '/location'),

-- PromoSection
('home', 'promo', 'title', 'section_title', 'Confiez-nous vos événements'),
('home', 'promo', 'text', 'section_description', 'L''assurance d''un événement réussi avec notre gamme complète de services et d''animations.'),

-- Footer
('global', 'footer', 'title', 'company_name', 'La Boutique de Minnie'),
('global', 'footer', 'text', 'company_description', 'L''assurance d''un événement réussi ! Créateur d''ambiances magiques pour tous vos événements spéciaux.')

ON CONFLICT DO NOTHING;

-- Politiques RLS (Row Level Security) pour sécuriser l'accès
ALTER TABLE site_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE media_library ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre la lecture publique du contenu
CREATE POLICY "Allow public read access to site_content" ON site_content
    FOR SELECT USING (true);

CREATE POLICY "Allow public read access to media_library" ON media_library
    FOR SELECT USING (true);

-- Politique pour permettre aux admins de modifier le contenu
CREATE POLICY "Allow admin full access to site_content" ON site_content
    FOR ALL USING (auth.jwt() ->> 'email' = '<EMAIL>');

CREATE POLICY "Allow admin full access to media_library" ON media_library
    FOR ALL USING (auth.jwt() ->> 'email' = '<EMAIL>');