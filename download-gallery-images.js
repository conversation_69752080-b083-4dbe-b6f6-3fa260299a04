import https from 'https';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Function to download an image
function downloadImage(url, filepath) {
  return new Promise((resolve, reject) => {
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download image, status code: ${response.statusCode}`));
        return;
      }

      const fileStream = fs.createWriteStream(filepath);
      response.pipe(fileStream);

      fileStream.on('finish', () => {
        fileStream.close();
        console.log(`Downloaded: ${filepath}`);
        resolve(filepath);
      });

      fileStream.on('error', (err) => {
        fs.unlink(filepath, () => {}); // Delete the file if there's an error
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// Create directories if they don't exist
const directories = [
  'public/images/gallery/photos',
  'public/images/gallery/photos/chateaux',
  'public/images/gallery/photos/arbres-noel',
  'public/images/gallery/photos/decos-theme',
  'public/images/gallery/photos/divertissements'
];

directories.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// List of images to download
const images = [
  // Main gallery photos
  {
    url: 'https://images.unsplash.com/photo-1653821355168-144695e5c0e6?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/gallery/photos/2023-06-05_bapteme_decoration.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1681157365387-3d578784f3af?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/gallery/photos/2023-07-15_anniversaire_chateau.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1469371670807-013ccf25f16a?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/gallery/photos/2023-08-20_mariage_decoration.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1652449302250-03e334d52330?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/gallery/photos/2023-09-10_animation_mascotte.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1533120921505-7f40f5237ee1?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/gallery/photos/2023-10-15_entreprise_conference.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1627278564229-905bebd3f41e?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/gallery/photos/2023-12-25_noel_decoration.jpg'
  },
  // Subcategory photos - Arbres de Noël
  {
    url: 'https://images.unsplash.com/photo-1607798748738-b15c40d33d57?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/gallery/photos/arbres-noel/2023-12-10_decoration-noel-hotel.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1607798422366-99aebc4b3699?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/gallery/photos/arbres-noel/2023-12-25_sapin-entreprise.jpg'
  },
  // Subcategory photos - Châteaux
  {
    url: 'https://images.unsplash.com/photo-1523438885200-e635ba2c371e?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/gallery/photos/chateaux/2023-07-15_chateau-princesse.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1588764703452-2b7cea0787d7?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/gallery/photos/chateaux/2023-07-20_bulle-transparente.jpg'
  },
  // Subcategory photos - Décos à thème
  {
    url: 'https://images.unsplash.com/photo-1565425518476-3229123699c5?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/gallery/photos/decos-theme/2023-06-05_bapteme-bleu.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1580994989611-9a9e5921343d?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/gallery/photos/decos-theme/2023-08-20_mariage-floral.jpg'
  },
  // Subcategory photos - Divertissements
  {
    url: 'https://images.unsplash.com/photo-1681157365406-4697096eba6a?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/gallery/photos/divertissements/2023-09-10_mascotte-anniversaire.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1737224319158-570f67b1f038?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/gallery/photos/divertissements/2023-12-15_pere-noel-animation.jpg'
  }
];

// Download all images
async function downloadAllImages() {
  for (const image of images) {
    try {
      await downloadImage(image.url, image.path);
    } catch (error) {
      console.error(`Error downloading ${image.path}:`, error.message);
    }
  }
}

downloadAllImages().then(() => {
  console.log('All gallery images downloaded successfully!');
}).catch(err => {
  console.error('Error downloading images:', err);
});
