import { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';
import { Calendar, Mail, Phone, MapPin, Users, MessageSquare, Check, X, Clock } from 'lucide-react';
import Button from '../ui/Button';
import { generateDevisPDF, downloadPDF } from '../../utils/pdfGenerator'; // Added for PDF export
import ExcelJS from 'exceljs'; // Added for Excel export

interface QuoteItem {
  name: string;
  quantity: number;
}

interface Quote {
  id: number;
  name: string;
  email: string;
  phone: string;
  event_type: string;
  event_date: string;
  location: string;
  guests_count: number;
  message: string;
  rental_items: QuoteItem[] | null; // Added rental_items
  status: 'pending' | 'contacted' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
}

const downloadFile = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

const QuotesManager = () => {
  const handleExcelExport = async (quote: Quote) => {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Devis Détails');

      // Define columns
      worksheet.columns = [
        { header: 'ID Devis', key: 'id', width: 10 },
        { header: 'Nom Client', key: 'name', width: 30 },
        { header: 'Email', key: 'email', width: 30 },
        { header: 'Téléphone', key: 'phone', width: 20 },
        { header: 'Type Événement', key: 'event_type', width: 25 },
        { header: 'Date Événement', key: 'event_date', width: 15 },
        { header: 'Lieu', key: 'location', width: 30 },
        { header: 'Nb Invités', key: 'guests_count', width: 15 },
        { header: 'Message', key: 'message', width: 50 },
        { header: 'Statut', key: 'status', width: 15 },
        { header: 'Date Création', key: 'created_at', width: 20 },
        { header: 'Matériel Loué', key: 'rental_items_list', width: 50 },
      ];

      // Add row for the quote
      worksheet.addRow({
        id: quote.id,
        name: quote.name,
        email: quote.email,
        phone: quote.phone || '-',
        event_type: quote.event_type,
        event_date: quote.event_date ? new Date(quote.event_date).toLocaleDateString('fr-FR') : '-',
        location: quote.location || '-',
        guests_count: quote.guests_count || '-',
        message: quote.message || '-',
        status: quote.status,
        created_at: new Date(quote.created_at).toLocaleString('fr-FR'),
        rental_items_list: quote.rental_items?.map(item => `${item.name} (Qté: ${item.quantity})`).join(',\n') || 'Aucun'
      });
      
      // Style header row
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFFFE0A8' } // Light orange
      };

      // Auto-wrap for message and rental items
      worksheet.getColumn('message').alignment = { wrapText: true };
      worksheet.getColumn('rental_items_list').alignment = { wrapText: true };

      // Generate Excel file
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      downloadFile(blob, `devis-${quote.id}-${quote.name.replace(/\s+/g, '_')}.xlsx`);
      toast.success('Fichier Excel du devis généré et téléchargé.');

    } catch (error) {
      console.error('Erreur lors de la génération du fichier Excel:', error);
      toast.error('Erreur lors de la génération du fichier Excel.');
    }
  };

  const handlePdfExport = async (quote: Quote) => {
    try {
      // We need to ensure generateDevisPDF can handle the full Quote object
      // including rental_items. This might require modification of generateDevisPDF.
      const DevisDataForPdf = {
        name: quote.name,
        email: quote.email,
        phone: quote.phone,
        eventType: quote.event_type,
        eventDate: quote.event_date, // Assuming generateDevisPDF can take this
        location: quote.location, // Assuming generateDevisPDF can take this
        guestsCount: quote.guests_count, // Assuming generateDevisPDF can take this
        message: quote.message,
        rentalItems: quote.rental_items, // Pass rental items
        // Add any other relevant fields from the Quote interface
      };
      // @ts-ignore // Temporary to bypass type checking until pdfGenerator is updated
      const pdfBlob = generateDevisPDF(DevisDataForPdf); 
      downloadPDF(pdfBlob, `devis-${quote.id}-${quote.name.replace(/\s+/g, '_')}.pdf`);
      toast.success('PDF du devis généré et téléchargé.');
    } catch (error) {
      console.error('Erreur lors de la génération du PDF:', error);
      toast.error('Erreur lors de la génération du PDF.');
    }
  };

  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'contacted' | 'approved' | 'rejected'>('all');

  useEffect(() => {
    fetchQuotes();
  }, [filter]);

  const fetchQuotes = async () => {
    try {
      setLoading(true);
      let query = supabase.from('quotes').select('*').order('created_at', { ascending: false });
      
      if (filter !== 'all') {
        query = query.eq('status', filter);
      }
      
      const { data, error } = await query;

      if (error) throw error;
      setQuotes(data || []);
    } catch (error: any) {
      console.error('Erreur lors du chargement des devis:', error);
      toast.error('Erreur lors du chargement des devis');
    } finally {
      setLoading(false);
    }
  };

  const updateQuoteStatus = async (id: number, status: Quote['status']) => {
    try {
      const { error } = await supabase
        .from('quotes')
        .update({ status })
        .eq('id', id);

      if (error) throw error;

      toast.success('Statut du devis mis à jour');
      fetchQuotes();
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour du statut:', error);
      toast.error('Erreur lors de la mise à jour');
    }
  };

  const getStatusBadge = (status: Quote['status']) => {
    switch (status) {
      case 'pending':
        return <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">En attente</span>;
      case 'contacted':
        return <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">Contacté</span>;
      case 'approved':
        return <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Approuvé</span>;
      case 'rejected':
        return <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Rejeté</span>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 border-solid">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Gestion des Devis</h3>
              <p className="mt-1 text-sm text-gray-500">
                Consultez et gérez les demandes de devis des clients
              </p>
            </div>
            <div className="flex space-x-2">
              <Button 
                size="sm" 
                variant={filter === 'all' ? 'primary' : 'outline'} 
                onClick={() => setFilter('all')}
              >
                Tous
              </Button>
              <Button 
                size="sm" 
                variant={filter === 'pending' ? 'primary' : 'outline'} 
                onClick={() => setFilter('pending')}
              >
                <Clock className="h-4 w-4 mr-1" />
                En attente
              </Button>
              <Button 
                size="sm" 
                variant={filter === 'contacted' ? 'primary' : 'outline'} 
                onClick={() => setFilter('contacted')}
              >
                <Phone className="h-4 w-4 mr-1" />
                Contactés
              </Button>
              <Button 
                size="sm" 
                variant={filter === 'approved' ? 'primary' : 'outline'} 
                onClick={() => setFilter('approved')}
              >
                <Check className="h-4 w-4 mr-1" />
                Approuvés
              </Button>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="p-6 text-center">
            <p>Chargement des devis...</p>
          </div>
        ) : quotes.length === 0 ? (
          <div className="p-6 text-center">
            <p>Aucun devis trouvé.</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {quotes.map((quote) => (
              <div key={quote.id} className="p-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 flex items-center">
                      {quote.name} {getStatusBadge(quote.status)}
                    </h4>
                    <p className="text-sm text-gray-500">
                      Demande reçue le {new Date(quote.created_at).toLocaleDateString('fr-FR')}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => updateQuoteStatus(quote.id, 'contacted')}
                    >
                      <Phone className="h-4 w-4 mr-1" />
                      Marquer comme contacté
                    </Button>
                    <Button 
                      size="sm" 
                      variant="primary" 
                      onClick={() => updateQuoteStatus(quote.id, 'approved')}
                    >
                      <Check className="h-4 w-4 mr-1" />
                      Approuver
                    </Button>
                    <Button 
                      size="sm" 
                      variant="secondary" 
                      onClick={() => updateQuoteStatus(quote.id, 'rejected')}
                    >
                      <X className="h-4 w-4 mr-1" />
                      Rejeter
                    </Button>
                    {/* Add Export Buttons Here */}
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => handlePdfExport(quote)} // Placeholder
                      className="ml-2"
                    >
                      Exporter PDF
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => handleExcelExport(quote)} // Placeholder
                      className="ml-2"
                    >
                      Exporter Excel
                    </Button>
                  </div>
                </div>

                <div className="mt-4 grid grid-cols-2 gap-4">
                  <div>
                    <p className="flex items-center text-sm text-gray-600">
                      <Mail className="h-4 w-4 mr-2" />
                      {quote.email}
                    </p>
                    <p className="flex items-center text-sm text-gray-600 mt-2">
                      <Phone className="h-4 w-4 mr-2" />
                      {quote.phone || 'Non spécifié'}
                    </p>
                    <p className="flex items-center text-sm text-gray-600 mt-2">
                      <MapPin className="h-4 w-4 mr-2" />
                      {quote.location || 'Non spécifié'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">
                      <strong>Type d'événement:</strong> {quote.event_type}
                    </p>
                    <p className="text-sm text-gray-600 mt-2">
                      <Calendar className="h-4 w-4 inline mr-2" />
                      <strong>Date:</strong> {quote.event_date ? new Date(quote.event_date).toLocaleDateString('fr-FR') : 'Non spécifiée'}
                    </p>
                    <p className="text-sm text-gray-600 mt-2">
                      <Users className="h-4 w-4 inline mr-2" />
                      <strong>Nombre d'invités:</strong> {quote.guests_count || 'Non spécifié'}
                    </p>
                  </div>
                </div>

                <div className="mt-4">
                  <p className="text-sm text-gray-600 flex items-start">
                    <MessageSquare className="h-4 w-4 mr-2 mt-1" />
                    <span>
                      <strong>Message:</strong><br />
                      {quote.message || 'Aucun message'}
                    </span>
                  </p>
                </div>

                {/* Display Rental Items */}
                {quote.rental_items && quote.rental_items.length > 0 && (
                  <div className="mt-4">
                    <p className="text-sm font-semibold text-gray-700">Matériel de location souhaité:</p>
                    <ul className="list-disc list-inside pl-4 mt-1 text-sm text-gray-600">
                      {quote.rental_items.map((item, index) => (
                        <li key={index}>{item.name} (Quantité: {item.quantity})</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default QuotesManager;
