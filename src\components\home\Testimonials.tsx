import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Quote } from 'lucide-react';

interface Testimonial {
  quote: string;
  name: string;
  role: string;
  imageUrl?: string;
}

const Testimonials = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  
  const testimonials: Testimonial[] = [
    {
      quote: "La Boutique de Minnie a transformé l'anniversaire de ma fille en un véritable conte de fées ! Tout était parfait, de la décoration aux petits détails.",
      name: "<PERSON>",
      role: "Maman comblée"
    },
    {
      quote: "Un grand merci pour la décoration magique de notre mariage. Tous nos invités étaient émerveillés par l'ambiance créée. Vraiment des professionnels à recommander !",
      name: "<PERSON> et <PERSON>",
      role: "Jeunes mariés"
    },
    {
      quote: "Notre conférence d'entreprise a été un succès grâce à la décoration élégante et professionnelle. Merci pour votre créativité et votre efficacité.",
      name: "<PERSON>",
      role: "Directeur marketing"
    }
  ];
  
  // Auto-advance carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);
    
    return () => clearInterval(interval);
  }, [testimonials.length]);
  
  const goToPrevious = () => {
    setActiveIndex((prevIndex) => 
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
  };
  
  const goToNext = () => {
    setActiveIndex((prevIndex) => 
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    );
  };

  return (
    <section className="py-16 bg-primary-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-display text-primary-700 mb-4">Ce que disent nos clients</h2>
          <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
            Découvrez les témoignages de clients satisfaits de nos services de décoration.
          </p>
        </div>
        
        <div className="relative max-w-4xl mx-auto">
          {/* Carousel */}
          <div className="overflow-hidden rounded-2xl bg-white shadow-lg">
            <div 
              className="transition-transform duration-500 ease-in-out flex"
              style={{ transform: `translateX(-${activeIndex * 100}%)` }}
            >
              {testimonials.map((testimonial, index) => (
                <div key={index} className="w-full flex-shrink-0 p-8 md:p-12">
                  <Quote className="text-primary-300 mb-4" size={48} />
                  <blockquote className="text-lg md:text-xl text-secondary-700 mb-6 italic">
                    "{testimonial.quote}"
                  </blockquote>
                  <div className="flex items-center">
                    <div className="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center">
                      <span className="text-primary-500 font-bold">
                        {testimonial.name.charAt(0)}
                      </span>
                    </div>
                    <div className="ml-4">
                      <p className="font-bold text-secondary-800">{testimonial.name}</p>
                      <p className="text-secondary-500 text-sm">{testimonial.role}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* Navigation buttons */}
          <button 
            onClick={goToPrevious}
            className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 bg-white rounded-full p-3 shadow-md hover:bg-primary-50 transition-colors"
            aria-label="Témoignage précédent"
          >
            <ChevronLeft className="text-primary-500" size={24} />
          </button>
          
          <button 
            onClick={goToNext}
            className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 bg-white rounded-full p-3 shadow-md hover:bg-primary-50 transition-colors"
            aria-label="Témoignage suivant"
          >
            <ChevronRight className="text-primary-500" size={24} />
          </button>
          
          {/* Indicators */}
          <div className="flex justify-center mt-6 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveIndex(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  activeIndex === index ? 'bg-primary-500' : 'bg-primary-200'
                }`}
                aria-label={`Aller au témoignage ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;