import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Ajouter des logs pour vérifier que les variables d'environnement sont correctement chargées
console.log("Supabase URL configurée:", supabaseUrl ? "Oui" : "Non");
console.log("Supabase Anon Key configurée:", supabaseAnonKey ? "Oui" : "Non");

export const supabase = createClient(supabaseUrl, supabaseAnonKey);
