import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get deployment target from command line arguments
const deploymentTarget = process.argv[2] || 'github-pages';

// Configuration
const config = {
  buildCommand: 'npm run build',
  distFolder: path.join(__dirname, 'dist'),
  deploymentTarget: deploymentTarget, // Options: 'github-pages', 'netlify', 'vercel'
};

// Function to execute shell commands
function executeCommand(command) {
  return new Promise((resolve, reject) => {
    console.log(`Executing: ${command}`);
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error: ${error.message}`);
        reject(error);
        return;
      }
      if (stderr) {
        console.log(`stderr: ${stderr}`);
      }
      console.log(`stdout: ${stdout}`);
      resolve(stdout);
    });
  });
}

// Function to build the project
async function buildProject() {
  try {
    console.log('Building project...');
    await executeCommand(config.buildCommand);
    console.log('Build completed successfully!');
    return true;
  } catch (error) {
    console.error('Build failed:', error);
    return false;
  }
}

// Function to prepare for GitHub Pages deployment
async function prepareForGitHubPages() {
  try {
    // Create or update CNAME file if needed
    // fs.writeFileSync(path.join(config.distFolder, 'CNAME'), 'laboutiquedeminnie.fr');

    // Create .nojekyll file to prevent GitHub from ignoring files that begin with an underscore
    fs.writeFileSync(path.join(config.distFolder, '.nojekyll'), '');

    console.log('GitHub Pages preparation completed!');
    return true;
  } catch (error) {
    console.error('GitHub Pages preparation failed:', error);
    return false;
  }
}

// Function to prepare for Netlify deployment
async function prepareForNetlify() {
  try {
    // Create netlify.toml file
    const netlifyConfig = `
[build]
  publish = "dist"
  command = "npm run build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
`;

    fs.writeFileSync(path.join(__dirname, 'netlify.toml'), netlifyConfig);
    console.log('Netlify preparation completed!');
    return true;
  } catch (error) {
    console.error('Netlify preparation failed:', error);
    return false;
  }
}

// Function to prepare for Vercel deployment
async function prepareForVercel() {
  try {
    // Create vercel.json file
    const vercelConfig = `
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": { "distDir": "dist" }
    }
  ],
  "routes": [
    { "handle": "filesystem" },
    { "src": "/.*", "dest": "/index.html" }
  ]
}
`;

    fs.writeFileSync(path.join(__dirname, 'vercel.json'), vercelConfig);
    console.log('Vercel preparation completed!');
    return true;
  } catch (error) {
    console.error('Vercel preparation failed:', error);
    return false;
  }
}

// Main function
async function deploy() {
  console.log('Starting deployment process...');

  // Build the project
  const buildSuccess = await buildProject();
  if (!buildSuccess) {
    console.error('Deployment aborted due to build failure.');
    return;
  }

  // Prepare for deployment based on target
  let prepSuccess = false;

  switch (config.deploymentTarget) {
    case 'github-pages':
      prepSuccess = await prepareForGitHubPages();
      break;
    case 'netlify':
      prepSuccess = await prepareForNetlify();
      break;
    case 'vercel':
      prepSuccess = await prepareForVercel();
      break;
    default:
      console.error(`Unknown deployment target: ${config.deploymentTarget}`);
      return;
  }

  if (!prepSuccess) {
    console.error('Deployment preparation failed.');
    return;
  }

  console.log(`
=================================================
Deployment preparation completed successfully!
=================================================

Your site is ready to be deployed to ${config.deploymentTarget}.

Next steps:
1. Commit your changes
2. Push to your repository
3. Follow the deployment instructions for ${config.deploymentTarget}

Thank you for using La Boutique de Minnie!
  `);
}

// Run the deployment process
deploy().catch(console.error);
