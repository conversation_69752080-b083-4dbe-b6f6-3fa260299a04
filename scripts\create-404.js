import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const distDir = path.resolve(__dirname, '../dist');

// Vérifier si le dossier dist existe
if (!fs.existsSync(distDir)) {
  console.error('Le dossier dist n\'existe pas. Exécutez d\'abord npm run build.');
  process.exit(1);
}

// Lire le contenu du fichier index.html
const indexPath = path.join(distDir, 'index.html');
const indexContent = fs.readFileSync(indexPath, 'utf8');

// C<PERSON>er le fichier 404.html avec le même contenu
const notFoundPath = path.join(distDir, '404.html');
fs.writeFileSync(notFoundPath, indexContent);

console.log('Fichier 404.html créé avec succès!');
