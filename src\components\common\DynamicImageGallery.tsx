import React, { useState, useEffect } from 'react';
import { getImagesFromFolder, getFolderName } from '../../utils/imageLoader';

interface DynamicImageGalleryProps {
  folderPath: string;
  className?: string;
}

const DynamicImageGallery: React.FC<DynamicImageGalleryProps> = ({ folderPath, className = '' }) => {
  const [images, setImages] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadImages = async () => {
      try {
        setLoading(true);
        const imageList = await getImagesFromFolder(folderPath);
        setImages(imageList);
        setError(null);
      } catch (err) {
        setError('Erreur lors du chargement des images');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    loadImages();
  }, [folderPath]);

  if (loading) {
    return <div className="flex justify-center items-center p-8">Chargement des images...</div>;
  }

  if (error) {
    return <div className="text-red-500 p-4">{error}</div>;
  }

  if (images.length === 0) {
    return <div className="p-4">Aucune image trouvée dans ce dossier.</div>;
  }

  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {images.map((imagePath, index) => {
        const folderName = getFolderName(imagePath);
        const alt = `Image ${folderName} ${index + 1}`;

        return (
          <div
            key={imagePath}
            className="relative overflow-hidden rounded-xl shadow-md transition-transform duration-300 hover:-translate-y-2 hover:shadow-lg group"
          >
            <img
              src={imagePath}
              alt={alt}
              loading="lazy"
              className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
              <p className="text-white p-4 font-medium">{folderName}</p>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default DynamicImageGallery;
