import { createServer } from 'vite';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import express from 'express';
import { createServer as createHttpServer } from 'http';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

function listFilesRecursively(dir, baseDir = '') {
  try {
    const files = [];
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const relativePath = path.join(baseDir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        files.push(...listFilesRecursively(fullPath, relativePath));
      } else {
        const ext = path.extname(item).toLowerCase();
        if (['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'].includes(ext)) {
          files.push(relativePath.replace(/\\/g, '/'));
        }
      }
    }

    return files;
  } catch (error) {
    console.error('Error reading directory:', error);
    return [];
  }
}

async function createDevServer() {
  const app = express();
  
  // Set CORS headers for all responses from /api/images
  app.use('/api/images', (req, res, next) => {
    res.set({
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    next();
  });

  // Handle OPTIONS requests
  app.options('/api/images', (req, res) => {
    res.status(200).json({ success: true });
  });

  // Handle GET requests to /api/images
  app.get('/api/images', (req, res) => {
    try {
      const folderParam = req.query.folder || '';
      const publicDir = path.resolve(__dirname, 'public');
      const targetDir = path.join(publicDir, folderParam);

      // Log request details for debugging
      console.log('Image request:', {
        folderParam,
        targetDir,
        publicDir
      });

      // Ensure the target directory exists and is within the public directory
      const normalizedTargetDir = path.normalize(targetDir);
      const normalizedPublicDir = path.normalize(publicDir);
      
      if (!normalizedTargetDir.startsWith(normalizedPublicDir)) {
        console.error('Invalid path attempted:', folderParam);
        return res.status(400).json({ 
          error: 'Invalid path',
          images: [] 
        });
      }

      // Check if directory exists
      if (!fs.existsSync(targetDir)) {
        console.error('Folder not found:', targetDir);
        return res.status(404).json({ 
          error: 'Folder not found',
          images: [] 
        });
      }

      const stat = fs.statSync(targetDir);
      if (!stat.isDirectory()) {
        console.error('Not a directory:', targetDir);
        return res.status(400).json({ 
          error: 'Specified path is not a directory',
          images: [] 
        });
      }

      const images = listFilesRecursively(targetDir)
        .map(file => `/${file}`);

      // Log response for debugging
      console.log('Sending response:', {
        imageCount: images.length,
        firstImage: images[0]
      });

      return res.json({ images });
    } catch (error) {
      console.error('Server error in /api/images:', error);
      return res.status(500).json({ 
        error: 'Server error while loading images',
        images: [] 
      });
    }
  });

  const vite = await createServer({
    configFile: path.resolve(__dirname, 'vite.config.ts'),
    server: {
      middlewareMode: true,
    }
  });

  // Apply Vite middleware after API routes
  app.use(vite.middlewares);

  // Ajouter une protection pour les routes d'administration
  app.use((req, res, next) => {
    const url = req.url;
    
    // Bloquer les tentatives d'accès direct à l'ancien chemin admin
    if (url === '/admin') {
      return res.redirect('/');
    }
    
    // Vérifier le token pour les nouvelles routes d'administration
    if (url.startsWith('/secure-admin/')) {
      const token = url.split('/')[2];
      // Importer la fonction de validation ou implémenter la vérification ici
      const isValidToken = token === "bm1pbm5pZTIwMjRhZG1pbg";
      
      if (!isValidToken) {
        return res.redirect('/');
      }
    }
    
    next();
  });

  // SPA route handling should come after API routes
  app.use((req, res, next) => {
    const url = req.url;
    
    // Skip API routes
    if (url.startsWith('/api/')) {
      return next();
    }

    const ext = path.extname(url);
    if (ext || url === '/') {
      return next();
    }

    const routes = ['admin', 'about', 'services', 'gallery', 'contact'];
    const urlPath = url.split('/')[1];

    if (routes.includes(urlPath)) {
      console.log(`SPA route detected: ${url} -> redirected to /`);
      req.url = '/';
    }

    next();
  });

  const server = createHttpServer(app);

  const PORT = 5173;
  server.listen(PORT, () => {
    console.log(`Server running at http://localhost:${PORT}/`);
    console.log(`Network: http://${getLocalIp()}:${PORT}/`);
  });
}

function getLocalIp() {
  try {
    return '************';
  } catch (error) {
    console.error('Error getting IP address:', error);
    return '127.0.0.1';
  }
}

createDevServer();
