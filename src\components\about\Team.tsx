import React from 'react';
import Card from '../ui/Card';

interface TeamMemberProps {
  name: string;
  role: string;
  bio: string;
}

const TeamMember: React.FC<TeamMemberProps> = ({ name, role, bio }) => {
  return (
    <Card className="flex flex-col items-center text-center" hoverEffect>
      <div className="w-24 h-24 bg-primary-100 rounded-full flex items-center justify-center mb-4">
        <span className="text-primary-600 text-2xl font-bold">{name.charAt(0)}</span>
      </div>
      <h3 className="text-xl font-bold text-secondary-800">{name}</h3>
      <p className="text-primary-500 mb-2">{role}</p>
      <p className="text-secondary-600">{bio}</p>
    </Card>
  );
};

const Team = () => {
  const team = [
    {
      name: "<PERSON>",
      role: "Fondatrice & Directrice Créative",
      bio: "Passionnée de décoration depuis son enfance, <PERSON> a fondé La Boutique de Minnie pour partager sa vision créative et sa passion pour l'univers Disney."
    },
    {
      name: "<PERSON>",
      role: "Décoratrice Senior",
      bio: "Experte en arrangements floraux et en design d'espace, Sophie apporte son œil artistique à chaque projet pour créer des décorations harmonieuses."
    },
    {
      name: "Thomas Moreau",
      role: "Responsable Logistique",
      bio: "Thomas veille à ce que chaque élément soit au bon endroit au bon moment, assurant une installation sans faille pour tous les événements."
    }
  ];

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-display text-primary-700 mb-4">Notre Équipe</h2>
          <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
            Rencontrez les personnes talentueuses qui transforment vos événements en moments magiques.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {team.map((member, index) => (
            <TeamMember 
              key={index}
              name={member.name}
              role={member.role}
              bio={member.bio}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Team;