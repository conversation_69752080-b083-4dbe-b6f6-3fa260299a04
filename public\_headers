/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  
  # IMPORTANT: Only uncomment the next line AFTER confirming HTTPS works properly
  # Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
  
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;

# Cache static assets
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# Cache images
/images/*
  Cache-Control: public, max-age=86400