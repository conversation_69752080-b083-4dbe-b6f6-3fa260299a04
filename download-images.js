import https from 'https';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Function to download an image
function downloadImage(url, filepath) {
  return new Promise((resolve, reject) => {
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download image, status code: ${response.statusCode}`));
        return;
      }

      const fileStream = fs.createWriteStream(filepath);
      response.pipe(fileStream);

      fileStream.on('finish', () => {
        fileStream.close();
        console.log(`Downloaded: ${filepath}`);
        resolve(filepath);
      });

      fileStream.on('error', (err) => {
        fs.unlink(filepath, () => {}); // Delete the file if there's an error
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// Create directories if they don't exist
const directories = [
  'public/images/home/<USER>',
  'public/images/home/<USER>',
  'public/images/home/<USER>',
  'public/images/services/categories',
  'public/images/gallery/photos',
  'public/images/gallery/photos/chateaux',
  'public/images/gallery/photos/arbres-noel',
  'public/images/gallery/photos/decos-theme',
  'public/images/gallery/photos/divertissements',
  'public/images/about/story',
  'public/images/home/<USER>'
];

directories.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// List of images to download
const images = [
  {
    url: 'https://images.unsplash.com/photo-1653821355736-0c2598d0a63e?ixlib=rb-4.1.0&q=80&w=1200',
    path: 'public/images/home/<USER>/hero.png'
  },
  {
    url: 'https://images.unsplash.com/photo-1530023367847-a683933f4172?ixlib=rb-4.1.0&q=80&w=1200',
    path: 'public/images/home/<USER>/promo-flyer.jpg'
  },
  // Gallery images
  {
    url: 'https://images.unsplash.com/photo-1588764703452-2b7cea0787d7?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/home/<USER>/gallery1.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1553704582-79417a629e83?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/home/<USER>/gallery2.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1737224319158-570f67b1f038?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/home/<USER>/gallery3.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1607798422366-99aebc4b3699?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/home/<USER>/gallery4.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1565425518476-3229123699c5?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/home/<USER>/gallery5.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1580994989611-9a9e5921343d?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/home/<USER>/gallery6.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1681157365387-3d578784f3af?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/home/<USER>/gallery7.jpg'
  },
  // Service categories
  {
    url: 'https://images.unsplash.com/photo-1681157365406-4697096eba6a?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/services/categories/enfant.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1563292749-0e070ca58b29?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/services/categories/adulte.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1469371670807-013ccf25f16a?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/services/categories/mariage.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1627278564229-905bebd3f41e?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/services/categories/noel.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1523438885200-e635ba2c371e?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/services/categories/chateau.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1652449302250-03e334d52330?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/services/categories/mascotte.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1464366400600-7168b8af9bc3?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/services/categories/stands.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1533120921505-7f40f5237ee1?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/services/categories/professionnel.jpg'
  },
  // Other images
  {
    url: 'https://images.unsplash.com/photo-1563292749-0e070ca58b29?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/about/story/story.jpg'
  },
  {
    url: 'https://images.unsplash.com/photo-1469371670807-013ccf25f16a?ixlib=rb-4.1.0&q=80&w=800',
    path: 'public/images/home/<USER>/testimonials.jpg'
  }
];

// Download all images
async function downloadAllImages() {
  for (const image of images) {
    try {
      await downloadImage(image.url, image.path);
    } catch (error) {
      console.error(`Error downloading ${image.path}:`, error.message);
    }
  }
}

downloadAllImages().then(() => {
  console.log('All images downloaded successfully!');
}).catch(err => {
  console.error('Error downloading images:', err);
});
