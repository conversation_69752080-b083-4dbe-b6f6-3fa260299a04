# Galerie Multimédia - La Boutique de Minnie

Ce dossier contient les médias (photos et vidéos) utilisés dans la galerie du site web de La Boutique de Minnie.

## Structure des dossiers

### Photos (`photos/`)
- `arbres-noel/` : 🎄 Nos arbres de Noël
- `decos-theme/` : 🎉 Nos décos à thème
- `chateaux/` : 🏰 Nos châteaux
- `divertissements/` : 🎠 Nos divertissements

### Vidéos (`videos/`)
- `arbres-noel/` : 🎄 Nos arbres de Noël
- `decos-theme/` : 🎉 Nos décos à thème
- `chateaux/` : 🏰 Nos châteaux
- `divertissements/` : 🎠 Nos divertissements

## Format des fichiers

### Photos
- Formats acceptés : JPG, PNG, WebP
- Dimensions recommandées : 1200x800 pixels (ratio 3:2)
- Taille maximale : 500 KB par image

### Vidéos
- Formats acceptés : MP4, WebM
- Résolution recommandée : 720p (1280x720) ou 1080p (1920x1080)
- Durée recommandée : 30 secondes à 2 minutes
- Taille maximale : 10 MB par vidéo
- Codec vidéo recommandé : H.264 (pour MP4) ou VP9 (pour WebM)
- Codec audio recommandé : AAC

## Nommage des fichiers

Pour faciliter l'organisation et le tri automatique, veuillez suivre ces conventions de nommage:

### Photos
Format: `YYYY-MM-DD_description.extension`
Exemple: `2023-12-25_decoration-sapin-entreprise.jpg`

### Vidéos
Format: `YYYY-MM-DD_description.extension`
Exemple: `2023-07-15_chateau-gonflable-anniversaire.mp4`

## Catégories thématiques

Les médias sont organisés selon ces catégories principales:

1. **🎄 Nos arbres de Noël** (`arbres-noel/`)
   - Décorations de Noël pour entreprises et particuliers
   - Sapins décorés
   - Événements de fin d'année

2. **🎉 Nos décos à thème** (`decos-theme/`)
   - Anniversaires
   - Baptêmes
   - Mariages
   - Événements d'entreprise
   - Décorations personnalisées

3. **🏰 Nos châteaux** (`chateaux/`)
   - Châteaux gonflables
   - Structures gonflables
   - Bulles transparentes
   - Trampolines

4. **🎠 Nos divertissements** (`divertissements/`)
   - Mascottes et personnages
   - Père Noël
   - Animations et spectacles
   - Stands gourmands
   - Ateliers créatifs

## Comment ajouter de nouveaux médias

1. Préparez votre fichier selon les recommandations ci-dessus
2. Nommez-le en suivant la convention de nommage
3. Placez-le dans le dossier approprié (`photos/` ou `videos/`)
4. Les nouveaux médias apparaîtront automatiquement dans la galerie du site

## Métadonnées (pour les développeurs)

Pour ajouter des métadonnées supplémentaires (comme des descriptions détaillées, des tags, etc.), vous pouvez créer un fichier JSON correspondant avec le même nom que le média.

Exemple:
- Média: `2023-12-25_noel_decoration-sapin.jpg`
- Métadonnées: `2023-12-25_noel_decoration-sapin.json`

Structure du fichier JSON:
```json
{
  "title": "Décoration de sapin de Noël",
  "description": "Décoration féerique de sapin de Noël pour un événement d'entreprise",
  "tags": ["noel", "sapin", "decoration", "entreprise"],
  "location": "Riviera - Palmeraie",
  "featured": true
}
```
