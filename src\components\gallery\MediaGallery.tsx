import React, { useState, useEffect } from 'react';
import VideoThumbnail from './VideoThumbnail';
import VideoPlayer from './VideoPlayer';

interface MediaItem {
  id: string;
  type: 'photo' | 'video';
  src: string;
  thumbnail: string;
  title: string;
  category: string;
  date: string;
}

const MediaGallery = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedVideo, setSelectedVideo] = useState<MediaItem | null>(null);
  const [showModal, setShowModal] = useState(false);

  // Simuler le chargement des médias
  useEffect(() => {
    const loadMedia = async () => {
      try {
        setLoading(true);

        // Dans un environnement réel, ces données viendraient d'une API
        const defaultMedia: MediaItem[] = [
          // 🎄 Nos arbres de Noël
          {
            id: '1',
            type: 'photo',
            src: '/images/gallery/photos/arbres-noel/2023-12-25_sapin-entreprise.jpg',
            thumbnail: '/images/gallery/photos/arbres-noel/2023-12-25_sapin-entreprise.jpg',
            title: 'Sapin de Noël décoré pour entreprise',
            category: 'arbres-noel',
            date: '2023-12-25'
          },
          {
            id: '2',
            type: 'photo',
            src: '/images/gallery/photos/arbres-noel/2023-12-10_decoration-noel-hotel.jpg',
            thumbnail: '/images/gallery/photos/arbres-noel/2023-12-10_decoration-noel-hotel.jpg',
            title: 'Décoration de Noël pour hôtel',
            category: 'arbres-noel',
            date: '2023-12-10'
          },

          // 🎉 Nos décos à thème
          {
            id: '3',
            type: 'photo',
            src: '/images/gallery/photos/decos-theme/2023-08-20_mariage-floral.jpg',
            thumbnail: '/images/gallery/photos/decos-theme/2023-08-20_mariage-floral.jpg',
            title: 'Décoration florale pour mariage',
            category: 'decos-theme',
            date: '2023-08-20'
          },
          {
            id: '4',
            type: 'photo',
            src: '/images/gallery/photos/decos-theme/2023-06-05_bapteme-bleu.jpg',
            thumbnail: '/images/gallery/photos/decos-theme/2023-06-05_bapteme-bleu.jpg',
            title: 'Décoration de baptême thème bleu',
            category: 'decos-theme',
            date: '2023-06-05'
          },

          // 🏰 Nos châteaux
          {
            id: '5',
            type: 'photo',
            src: '/images/gallery/photos/chateaux/2023-07-15_chateau-princesse.jpg',
            thumbnail: '/images/gallery/photos/chateaux/2023-07-15_chateau-princesse.jpg',
            title: 'Château gonflable thème princesse',
            category: 'chateaux',
            date: '2023-07-15'
          },
          {
            id: '6',
            type: 'video',
            src: '/images/gallery/videos/chateaux/2023-07-20_bulle-transparente.mp4',
            thumbnail: '/images/gallery/photos/chateaux/2023-07-20_bulle-transparente.jpg',
            title: 'Bulle transparente avec ballons',
            category: 'chateaux',
            date: '2023-07-20'
          },

          // 🎠 Nos divertissements
          {
            id: '7',
            type: 'photo',
            src: '/images/gallery/photos/divertissements/2023-09-10_mascotte-anniversaire.jpg',
            thumbnail: '/images/gallery/photos/divertissements/2023-09-10_mascotte-anniversaire.jpg',
            title: 'Mascotte pour anniversaire enfant',
            category: 'divertissements',
            date: '2023-09-10'
          },
          {
            id: '8',
            type: 'video',
            src: '/images/gallery/videos/divertissements/2023-12-15_pere-noel-animation.mp4',
            thumbnail: '/images/gallery/photos/divertissements/2023-12-15_pere-noel-animation.jpg',
            title: 'Animation Père Noël pour entreprise',
            category: 'divertissements',
            date: '2023-12-15'
          }
        ];

        setMediaItems(defaultMedia);
      } catch (error) {
        console.error('Erreur lors du chargement des médias:', error);
      } finally {
        setLoading(false);
      }
    };

    loadMedia();
  }, []);

  const filters = [
    { id: 'all', label: 'Tous' },
    { id: 'photo', label: 'Photos' },
    { id: 'video', label: 'Vidéos' },
    { id: 'arbres-noel', label: '🎄 Arbres de Noël' },
    { id: 'decos-theme', label: '🎉 Décos à thème' },
    { id: 'chateaux', label: '🏰 Châteaux' },
    { id: 'divertissements', label: '🎠 Divertissements' }
  ];

  const filteredMedia = mediaItems.filter(item => {
    if (activeFilter === 'all') return true;
    if (activeFilter === 'photo' || activeFilter === 'video') return item.type === activeFilter;

    // Filtrer par catégorie thématique
    const folderPath = item.src.split('/');
    const categoryFolder = folderPath[folderPath.length - 2]; // Obtenir le nom du dossier parent
    return categoryFolder === activeFilter;
  });

  const handleVideoClick = (item: MediaItem) => {
    setSelectedVideo(item);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedVideo(null);
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="text-center mb-12">
        <h2 className="text-3xl md:text-4xl font-display text-primary-700 mb-4">Notre Galerie</h2>
        <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
          Découvrez nos réalisations en photos et vidéos pour tous types d'événements.
        </p>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap justify-center gap-2 mb-8">
        {filters.map(filter => (
          <button
            type="button"
            key={filter.id}
            onClick={() => setActiveFilter(filter.id)}
            className={`px-4 py-2 rounded-full text-sm transition-colors ${
              activeFilter === filter.id
                ? 'bg-primary-500 text-white'
                : 'bg-secondary-100 text-secondary-600 hover:bg-secondary-200'
            }`}
          >
            {filter.label}
          </button>
        ))}
      </div>

      {/* Gallery Grid */}
      {loading ? (
        <div className="flex justify-center items-center p-8">Chargement des médias...</div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMedia.map((item) => (
            <div key={item.id}>
              {item.type === 'photo' ? (
                <div className="relative overflow-hidden rounded-xl shadow-md transition-transform duration-300 hover:-translate-y-2 hover:shadow-lg group">
                  <img
                    src={item.src}
                    alt={item.title}
                    loading="lazy"
                    className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                    <p className="text-white p-4 font-medium">{item.title}</p>
                  </div>
                </div>
              ) : (
                <VideoThumbnail
                  src={item.src}
                  poster={item.thumbnail}
                  title={item.title}
                  onClick={() => handleVideoClick(item)}
                />
              )}
            </div>
          ))}
        </div>
      )}

      {/* Video Modal */}
      {showModal && selectedVideo && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl p-6 max-w-4xl w-full">
            <VideoPlayer
              src={selectedVideo.src}
              poster={selectedVideo.thumbnail}
              title={selectedVideo.title}
              onClose={closeModal}
              isModal={true}
            />
            <div className="mt-4">
              <h3 className="text-xl font-bold text-secondary-800">{selectedVideo.title}</h3>
              <p className="text-secondary-600 mt-1">Catégorie: {selectedVideo.category}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaGallery;
