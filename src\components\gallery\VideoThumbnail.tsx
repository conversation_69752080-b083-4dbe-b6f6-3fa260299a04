import React from 'react';
import { Play } from 'lucide-react';

interface VideoThumbnailProps {
  src: string;
  poster: string;
  title: string;
  onClick: () => void;
}

const VideoThumbnail: React.FC<VideoThumbnailProps> = ({ src, poster, title, onClick }) => {
  return (
    <div
      className="relative overflow-hidden rounded-xl shadow-md transition-transform duration-300 hover:-translate-y-2 hover:shadow-lg cursor-pointer group"
      onClick={onClick}
    >
      <div className="relative">
        <img
          src={poster}
          alt={title}
          loading="lazy"
          className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
        />
        <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
          <div className="w-12 h-12 bg-primary-500 bg-opacity-80 rounded-full flex items-center justify-center group-hover:bg-opacity-100 transition-all">
            <Play size={24} className="text-white ml-1" />
          </div>
        </div>
      </div>
      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
        <div className="p-4 w-full">
          <p className="text-white font-medium truncate">{title}</p>
        </div>
      </div>
    </div>
  );
};

export default VideoThumbnail;
