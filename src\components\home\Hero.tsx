import Button from '../ui/Button';
import { Link } from '../ui/Link';
import { useContent } from '../../hooks/useContent';

const Hero = () => {
  const { content, loading } = useContent('home', 'hero');

  // Valeurs par défaut en cas de chargement ou d'erreur
  const mainTitle = content.main_title || 'Créez des Moments Magiques';
  const mainDescription = content.main_description || 'La Boutique de Minnie transforme vos événements en expériences inoubliables avec des décorations féeriques et des animations pour tous.';
  const tagline = content.tagline || "L'assurance d'un événement réussi !";
  const heroImage = content.hero_image || '/images/home/<USER>/hero.png';
  const primaryButtonText = content.primary_button_text || 'Demander un devis';
  const primaryButtonLink = content.primary_button_link || '/devis';
  const secondaryButtonText = content.secondary_button_text || "Location d'articles";
  const secondaryButtonLink = content.secondary_button_link || '/location';

  if (loading) {
    return (
      <section className="relative pt-24 pb-16 md:pt-32 md:pb-24 overflow-hidden">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-10 md:mb-0 md:pr-10">
              <div className="animate-pulse">
                <div className="h-16 bg-gray-200 rounded mb-6"></div>
                <div className="h-6 bg-gray-200 rounded mb-3"></div>
                <div className="h-6 bg-gray-200 rounded mb-8"></div>
                <div className="flex gap-4">
                  <div className="h-12 w-32 bg-gray-200 rounded"></div>
                  <div className="h-12 w-32 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
            <div className="md:w-1/2">
              <div className="animate-pulse">
                <div className="h-64 bg-gray-200 rounded-2xl"></div>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="relative pt-24 pb-16 md:pt-32 md:pb-24 overflow-hidden">
      {/* Background with polka dots */}
      <div className="absolute inset-0 opacity-10 pointer-events-none">
        <div className="absolute inset-0 polka-dot-bg"></div>
      </div>

      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row items-center">
          <div className="md:w-1/2 mb-10 md:mb-0 md:pr-10">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-display text-primary-700 mb-6 leading-tight">
              {mainTitle.split(' ').slice(0, -1).join(' ')} <span className="relative">
                {mainTitle.split(' ').slice(-1)[0]}
                <span className="absolute -bottom-2 left-0 w-full h-1 bg-primary-400 rounded-full"></span>
              </span>
            </h1>

            <p className="text-lg md:text-xl text-secondary-700 mb-3 leading-relaxed">
              {mainDescription}
            </p>

            <p className="text-xl md:text-2xl font-display text-primary-600 mb-8">
              {tagline}
            </p>

            <div className="flex flex-col sm:flex-row gap-4">
              <Link to={primaryButtonLink}>
                <Button size="lg">
                  {primaryButtonText}
                </Button>
              </Link>
              <Link to={secondaryButtonLink}>
                <Button variant="outline" size="lg">
                  {secondaryButtonText}
                </Button>
              </Link>
            </div>
          </div>

          <div className="md:w-1/2 relative">
            <div className="relative rounded-2xl overflow-hidden shadow-xl animate-float">
              <img
                src={heroImage}
                alt="Décoration féérique pour événement"
                className="w-full h-auto rounded-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary-500/20 to-transparent"></div>
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-6 -right-6 w-16 h-16 rounded-full bg-primary-300 opacity-70"></div>
            <div className="absolute -bottom-8 -left-8 w-20 h-20 rounded-full bg-primary-400 opacity-50"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;