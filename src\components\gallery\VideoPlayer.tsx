import React, { useState, useRef } from 'react';
import { Play, Pause, Volume2, VolumeX, Maximize, X } from 'lucide-react';

interface VideoPlayerProps {
  src: string;
  poster?: string;
  title: string;
  onClose?: () => void;
  isModal?: boolean;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ 
  src, 
  poster, 
  title, 
  onClose, 
  isModal = false 
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [progress, setProgress] = useState(0);
  const videoRef = useRef<HTMLVideoElement>(null);

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const progress = (videoRef.current.currentTime / videoRef.current.duration) * 100;
      setProgress(progress);
    }
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (videoRef.current) {
      const progressBar = e.currentTarget;
      const rect = progressBar.getBoundingClientRect();
      const pos = (e.clientX - rect.left) / progressBar.offsetWidth;
      videoRef.current.currentTime = pos * videoRef.current.duration;
    }
  };

  const handleFullscreen = () => {
    if (videoRef.current) {
      if (videoRef.current.requestFullscreen) {
        videoRef.current.requestFullscreen();
      }
    }
  };

  return (
    <div className={`relative ${isModal ? 'w-full max-w-4xl' : 'w-full'}`}>
      {isModal && (
        <button
          type="button"
          onClick={onClose}
          className="absolute -top-4 -right-4 z-10 bg-white rounded-full p-1 shadow-md hover:bg-gray-100"
          aria-label="Fermer"
        >
          <X size={24} className="text-secondary-800" />
        </button>
      )}
      
      <div className="relative overflow-hidden rounded-xl bg-black">
        <video
          ref={videoRef}
          src={src}
          poster={poster}
          className="w-full h-auto"
          onTimeUpdate={handleTimeUpdate}
          onEnded={() => setIsPlaying(false)}
          onClick={togglePlay}
        />
        
        {/* Overlay for title when not playing */}
        {!isPlaying && (
          <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
            <div className="text-center">
              <button
                type="button"
                onClick={togglePlay}
                className="w-16 h-16 bg-primary-500 bg-opacity-80 rounded-full flex items-center justify-center mb-4 hover:bg-opacity-100 transition-all"
                aria-label="Lire la vidéo"
              >
                <Play size={32} className="text-white ml-1" />
              </button>
              <h3 className="text-white text-lg font-medium px-4">{title}</h3>
            </div>
          </div>
        )}
        
        {/* Controls */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
          {/* Progress bar */}
          <div 
            className="w-full h-1 bg-gray-600 rounded-full mb-3 cursor-pointer"
            onClick={handleProgressClick}
          >
            <div 
              className="h-full bg-primary-500 rounded-full"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                type="button"
                onClick={togglePlay}
                className="text-white mr-4 hover:text-primary-300 transition-colors"
                aria-label={isPlaying ? "Pause" : "Lecture"}
              >
                {isPlaying ? <Pause size={20} /> : <Play size={20} />}
              </button>
              
              <button
                type="button"
                onClick={toggleMute}
                className="text-white hover:text-primary-300 transition-colors"
                aria-label={isMuted ? "Activer le son" : "Couper le son"}
              >
                {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
              </button>
            </div>
            
            <button
              type="button"
              onClick={handleFullscreen}
              className="text-white hover:text-primary-300 transition-colors"
              aria-label="Plein écran"
            >
              <Maximize size={20} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoPlayer;
