import React, { useState, useEffect } from 'react';
import DynamicImageGallery from '../common/DynamicImageGallery';

interface GalleryImage {
  url: string;
  alt: string;
  category: string;
}

const Gallery = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [loading, setLoading] = useState(true);

  // Charger les images dynamiquement
  useEffect(() => {
    const loadImages = async () => {
      try {
        // Simuler le chargement des images depuis l'API
        // Dans un environnement réel, cela viendrait de l'API
        const defaultImages: GalleryImage[] = [
          {
            url: "/images/home/<USER>/promo-flyer.jpg",
            alt: "Nos services - Châteaux gonflables, bulles, arbre de noël, mascottes et plus",
            category: "all"
          },
          {
            url: "/images/home/<USER>/gallery1.jpg",
            alt: "Bulle transparente rose avec ballons pour événement enfant",
            category: "enfant"
          },
          {
            url: "/images/home/<USER>/gallery2.jpg",
            alt: "Table décorée avec centre de table floral et accessoires roses",
            category: "diner"
          },
          {
            url: "/images/home/<USER>/gallery3.jpg",
            alt: "Décor d'anniversaire avec thème papillon et ballons",
            category: "enfant"
          },
          {
            url: "/images/home/<USER>/gallery4.jpg",
            alt: "Décoration événement adulte",
            category: "adulte"
          },
          {
            url: "/images/home/<USER>/gallery5.jpg",
            alt: "Décoration de conférence",
            category: "conference"
          },
          {
            url: "/images/home/<USER>/gallery6.jpg",
            alt: "Aire de jeux pour enfants",
            category: "enfant"
          }
        ];

        setImages(defaultImages);
      } catch (error) {
        console.error('Erreur lors du chargement des images:', error);
      } finally {
        setLoading(false);
      }
    };

    loadImages();
  }, []);

  const filters = [
    { id: 'all', label: 'Tous' },
    { id: 'enfant', label: 'Enfants' },
    { id: 'adulte', label: 'Adultes' },
    { id: 'mariage', label: 'Mariages' },
    { id: 'diner', label: 'Dîners' },
    { id: 'conference', label: 'Conférences' }
  ];

  const filteredImages = activeFilter === 'all'
    ? images
    : images.filter(image => image.category === activeFilter);

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-display text-primary-700 mb-4">Nos Réalisations</h2>
          <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
            Découvrez nos plus belles décorations événementielles réalisées avec passion.
          </p>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {filters.map(filter => (
            <button
              type="button"
              key={filter.id}
              onClick={() => setActiveFilter(filter.id)}
              className={`px-4 py-2 rounded-full text-sm transition-colors ${
                activeFilter === filter.id
                  ? 'bg-primary-500 text-white'
                  : 'bg-secondary-100 text-secondary-600 hover:bg-secondary-200'
              }`}
            >
              {filter.label}
            </button>
          ))}
        </div>

        {/* Gallery Grid */}
        {loading ? (
          <div className="flex justify-center items-center p-8">Chargement des images...</div>
        ) : (
          <>
            {/* Images statiques (pour la compatibilité) */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredImages.map((image, index) => (
                <div
                  key={index}
                  className="relative overflow-hidden rounded-xl shadow-md transition-transform duration-300 hover:-translate-y-2 hover:shadow-lg group"
                >
                  <img
                    src={image.url}
                    alt={image.alt}
                    loading="lazy"
                    className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                    <p className="text-white p-4 font-medium">{image.alt}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* Images dynamiques (nouvelles images ajoutées via GitHub) */}
            <div className="mt-16">
              <h3 className="text-2xl font-display text-primary-700 mb-8 text-center">Nouvelles Réalisations</h3>
              <DynamicImageGallery folderPath="images/home/<USER>" />
            </div>
          </>
        )}
      </div>
    </section>
  );
};

export default Gallery;