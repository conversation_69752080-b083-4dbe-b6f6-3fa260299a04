import { useState, useEffect } from 'react';
import { Menu, X } from 'lucide-react';
import { Link } from '../ui/Link';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <header
      className={`fixed w-full top-0 z-50 transition-all duration-300 ${
        isScrolled ? 'bg-white shadow-md py-2' : 'bg-transparent py-4'
      }`}
    >
      <div className="container mx-auto px-4 flex justify-between items-center">
        <div className="flex items-center">
          <Link to="/" className="flex items-center">
            <img 
              src="/images/common/logo/minnie-icon.svg" 
              alt="La Boutique de Minnie"
              className="h-16 w-auto"
            />
            <div className="ml-3">
              <span className="text-2xl font-display text-primary-700 block">La Boutique de Minnie</span>
              <span className="text-sm text-secondary-600 font-medium">Évènementiel enfants et adultes</span>
            </div>
          </Link>
        </div>

        {/* Desktop Menu */}
        <nav className="hidden md:flex items-center space-x-8">
          <Link to="/" className="text-secondary-800 hover:text-primary-600 font-medium transition-colors">
            Accueil
          </Link>
          <Link to="/services" className="text-secondary-800 hover:text-primary-600 font-medium transition-colors">
            Nos services
          </Link>
          <Link to="/devis" className="text-secondary-800 hover:text-primary-600 font-medium transition-colors">
            Devis
          </Link>
          <Link to="/location" className="text-secondary-800 hover:text-primary-600 font-medium transition-colors">
            Location
          </Link>
          <Link to="/gallery" className="text-secondary-800 hover:text-primary-600 font-medium transition-colors">
            Galerie
          </Link>
          <Link to="/about" className="text-secondary-800 hover:text-primary-600 font-medium transition-colors">
            À propos
          </Link>
          <Link
            to="/contact"
            className="bg-primary-500 hover:bg-primary-600 text-white px-5 py-2 rounded-full transition-colors font-medium"
          >
            Contacts
          </Link>
        </nav>

        {/* Mobile Menu Button */}
        <button
          type="button"
          className="md:hidden text-secondary-800"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          aria-label={isMenuOpen ? "Fermer le menu" : "Ouvrir le menu"}
        >
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white w-full py-4 shadow-lg">
          <nav className="container mx-auto px-4 flex flex-col space-y-4">
            <Link
              to="/"
              className="text-secondary-800 hover:text-primary-600 font-medium transition-colors py-2"
              onClick={() => setIsMenuOpen(false)}
            >
              Accueil
            </Link>
            <Link
              to="/services"
              className="text-secondary-800 hover:text-primary-600 font-medium transition-colors py-2"
              onClick={() => setIsMenuOpen(false)}
            >
              Nos services
            </Link>
            <Link
              to="/devis"
              className="text-secondary-800 hover:text-primary-600 font-medium transition-colors py-2"
              onClick={() => setIsMenuOpen(false)}
            >
              Devis
            </Link>
            <Link
              to="/location"
              className="text-secondary-800 hover:text-primary-600 font-medium transition-colors py-2"
              onClick={() => setIsMenuOpen(false)}
            >
              Location
            </Link>
            <Link
              to="/gallery"
              className="text-secondary-800 hover:text-primary-600 font-medium transition-colors py-2"
              onClick={() => setIsMenuOpen(false)}
            >
              Galerie
            </Link>
            <Link
              to="/about"
              className="text-secondary-800 hover:text-primary-600 font-medium transition-colors py-2"
              onClick={() => setIsMenuOpen(false)}
            >
              À propos
            </Link>
            <Link
              to="/contact"
              className="bg-primary-500 hover:bg-primary-600 text-white px-5 py-2 rounded-full transition-colors font-medium inline-block text-center"
              onClick={() => setIsMenuOpen(false)}
            >
              Contacts
            </Link>
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;
