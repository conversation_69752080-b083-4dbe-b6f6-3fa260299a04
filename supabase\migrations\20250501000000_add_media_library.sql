-- Migration pour ajouter la gestion des médias
CREATE TABLE IF NOT EXISTS media_library (
  id SERIAL PRIMARY KEY,
  url TEXT NOT NULL,
  alt_text TEXT,
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Politiques RLS pour sécuriser l'accès
ALTER TABLE media_library ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre aux admins de voir et modifier les médias
CREATE POLICY "Allow admin full access to media_library" ON media_library
    FOR ALL USING (auth.jwt() ->> 'email' = '<EMAIL>');