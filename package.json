{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "vite", "dev:server": "node server.js", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "deploy": "node deploy.js", "deploy:github": "node deploy.js github-pages", "deploy:netlify": "node deploy.js netlify", "deploy:vercel": "node deploy.js vercel"}, "dependencies": {"@supabase/supabase-js": "^2.39.3", "date-fns": "^3.3.1", "exceljs": "^4.4.0", "express": "^5.1.0", "jspdf": "^2.5.1", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-router-dom": "^7.6.2"}, "devDependencies": {"@types/jspdf": "^2.0.0", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.1.4"}}