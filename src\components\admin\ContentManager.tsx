import { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';
import { Save, Edit, Image, Type, Link as LinkIcon } from 'lucide-react';
import Button from '../ui/Button';

interface ContentItem {
  id: number;
  page_name: string;
  section_name: string;
  content_type: string;
  content_key: string;
  content_value: string;
  updated_at: string;
}

interface MediaItem {
  id: number;
  url: string;
  alt_text: string;
  uploaded_at: string;
}

const ContentManager = () => {
  const [activeTab, setActiveTab] = useState('content');
  const [contentItems, setContentItems] = useState<ContentItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingItem, setEditingItem] = useState<ContentItem | null>(null);
  const [editValue, setEditValue] = useState('');

  useEffect(() => {
    if (activeTab === 'content') {
      fetchContent();
    } else {
      fetchMedia();
    }
  }, [activeTab]);

  const fetchContent = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('site_content')
        .select('*')
        .order('page_name', { ascending: true })
        .order('section_name', { ascending: true });

      if (error) throw error;
      setContentItems(data || []);
    } catch (error: any) {
      console.error('Erreur lors du chargement du contenu:', error);
      toast.error('Erreur lors du chargement du contenu');
    } finally {
      setLoading(false);
    }
  };

  const fetchMedia = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('media_library')
        .select('*')
        .order('uploaded_at', { ascending: false });

      if (error) throw error;
      setMediaItems(data || []);
    } catch (error: any) {
      console.error('Erreur lors du chargement des médias:', error);
      toast.error('Erreur lors du chargement des médias');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (item: ContentItem) => {
    setEditingItem(item);
    setEditValue(item.content_value);
  };

  const handleSave = async () => {
    if (!editingItem) return;

    try {
      const { error } = await supabase
        .from('site_content')
        .update({ content_value: editValue })
        .eq('id', editingItem.id);

      if (error) throw error;

      toast.success('Contenu mis à jour avec succès');
      setEditingItem(null);
      setEditValue('');
      fetchContent();
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour:', error);
      toast.error('Erreur lors de la mise à jour');
    }
  };

  const handleCancel = () => {
    setEditingItem(null);
    setEditValue('');
  };

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'title':
        return <Type className="h-4 w-4" />;
      case 'text':
        return <Edit className="h-4 w-4" />;
      case 'image':
        return <Image className="h-4 w-4" />;
      case 'button':
        return <LinkIcon className="h-4 w-4" />;
      default:
        return <Edit className="h-4 w-4" />;
    }
  };

  const getContentTypeColor = (type: string) => {
    switch (type) {
      case 'title':
        return 'bg-blue-100 text-blue-800';
      case 'text':
        return 'bg-green-100 text-green-800';
      case 'image':
        return 'bg-purple-100 text-purple-800';
      case 'button':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // const formatFileSize = (bytes: number) => {
  //   if (bytes === 0) return '0 Bytes';
  //   const k = 1024;
  //   const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  //   const i = Math.floor(Math.log(bytes) / Math.log(k));
  //   return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  // };

  const renderContentTab = () => (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 border-solid">
          <h3 className="text-lg font-medium text-gray-900">Gestion du contenu du site</h3>
          <p className="mt-1 text-sm text-gray-500">
            Modifiez les titres, textes et liens de votre site web
          </p>
        </div>

        {loading ? (
          <div className="p-6">
            <div className="animate-pulse space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {contentItems.map((item) => (
              <div key={item.id} className="p-6 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getContentTypeColor(item.content_type)}`}>
                        {getContentTypeIcon(item.content_type)}
                        <span className="ml-1 capitalize">{item.content_type}</span>
                      </span>
                      <span className="text-sm text-gray-500">
                        {item.page_name} → {item.section_name} → {item.content_key}
                      </span>
                    </div>

                    {editingItem?.id === item.id ? (
                      <div className="space-y-3">
                        {item.content_type === 'text' || item.content_type === 'title' ? (
                          <textarea
                            value={editValue}
                            onChange={(e) => setEditValue(e.target.value)}
                            className="w-full p-3 border border-gray-300 border-solid rounded-md focus:ring-primary-500 focus:border-primary-500"
                            rows={item.content_type === 'title' ? 2 : 4}
                            aria-label={`Modifier ${item.content_key}`}
                            placeholder={`Entrez le ${item.content_type === 'title' ? 'titre' : 'texte'}`}
                          />
                        ) : (
                          <input
                            type="text"
                            value={editValue}
                            onChange={(e) => setEditValue(e.target.value)}
                            className="w-full p-3 border border-gray-300 border-solid rounded-md focus:ring-primary-500 focus:border-primary-500"
                            aria-label={`Modifier ${item.content_key}`}
                            placeholder={`Entrez ${item.content_type === 'image' ? 'le chemin de l\'image' : 'la valeur'}`}
                          />
                        )}
                        <div className="flex space-x-2">
                          <Button size="sm" onClick={handleSave}>
                            <Save className="h-4 w-4 mr-1" />
                            Sauvegarder
                          </Button>
                          <Button size="sm" variant="outline" onClick={handleCancel}>
                            Annuler
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <p className="text-gray-900">
                          {item.content_type === 'image' ? (
                            <span className="text-blue-600 underline">{item.content_value}</span>
                          ) : (
                            item.content_value
                          )}
                        </p>
                        <p className="text-xs text-gray-500">
                          Dernière modification: {new Date(item.updated_at).toLocaleString('fr-FR')}
                        </p>
                      </div>
                    )}
                  </div>

                  {editingItem?.id !== item.id && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEdit(item)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Modifier
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  const renderMediaTab = () => {
    const [uploadFile, setUploadFile] = useState<File | null>(null);
    const [uploading, setUploading] = useState(false);
    const [altText, setAltText] = useState('');
    const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
    const [mediaLoading, setMediaLoading] = useState(true);

    useEffect(() => {
      fetchMedia();
    }, []);

    const fetchMedia = async () => {
      try {
        setMediaLoading(true);
        const { data, error } = await supabase
          .from('media_library')
          .select('*')
          .order('uploaded_at', { ascending: false });

        if (error) throw error;
        setMediaItems(data || []);
      } catch (error: any) {
        console.error('Erreur lors du chargement des médias:', error);
        toast.error('Erreur lors du chargement des médias');
      } finally {
        setMediaLoading(false);
      }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files.length > 0) {
        setUploadFile(e.target.files[0]);
      }
    };

    const handleUpload = async () => {
      if (!uploadFile) {
        toast.error('Veuillez sélectionner un fichier');
        return;
      }

      try {
        setUploading(true);
        
        // Upload to storage
        const fileExt = uploadFile.name.split('.').pop();
        const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
        const filePath = `media/${fileName}`;
        
        const { error: uploadError } = await supabase.storage
          .from('public')
          .upload(filePath, uploadFile);
        
        if (uploadError) throw uploadError;
        
        // Get public URL
        const { data: publicURL } = supabase.storage
          .from('public')
          .getPublicUrl(filePath);
        
        // Save to database
        const { error: dbError } = await supabase
          .from('media_library')
          .insert([
            { 
              url: publicURL.publicUrl, 
              alt_text: altText || uploadFile.name,
            }
          ]);
        
        if (dbError) throw dbError;
        
        toast.success('Média téléchargé avec succès');
        setUploadFile(null);
        setAltText('');
        fetchMedia();
      } catch (error: any) {
        console.error('Erreur lors du téléchargement:', error);
        toast.error('Erreur lors du téléchargement');
      } finally {
        setUploading(false);
      }
    };

    return (
      <div>
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-md font-medium mb-2">Ajouter un nouveau média</h4>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Fichier
              </label>
              <input
                type="file"
                onChange={handleFileChange}
                aria-label="Sélectionner un fichier"
                className="block w-full text-sm text-gray-500
                  file:mr-4 file:py-2 file:px-4
                  file:rounded-md file:border-0
                  file:text-sm file:font-semibold
                  file:bg-primary-50 file:text-primary-700
                  hover:file:bg-primary-100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Texte alternatif
              </label>
              <input
                type="text"
                value={altText}
                onChange={(e) => setAltText(e.target.value)}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="Description de l'image"
              />
            </div>
          </div>
          <div className="mt-4">
            <Button
              variant="primary"
              size="sm"
              onClick={handleUpload}
              disabled={uploading || !uploadFile}
            >
              {uploading ? 'Téléchargement...' : 'Télécharger'}
            </Button>
          </div>
        </div>

        {mediaLoading ? (
          <div className="text-center py-4">Chargement des médias...</div>
        ) : mediaItems.length === 0 ? (
          <div className="text-center py-4">Aucun média trouvé</div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {mediaItems.map((item) => (
              <div key={item.id} className="border rounded-lg overflow-hidden">
                <img 
                  src={item.url} 
                  alt={item.alt_text} 
                  className="w-full h-32 object-cover"
                />
                <div className="p-2">
                  <p className="text-xs truncate">{item.alt_text}</p>
                  <div className="flex justify-between mt-2">
                    <button 
                      className="text-xs text-blue-600 hover:text-blue-800"
                      onClick={() => {
                        navigator.clipboard.writeText(item.url);
                        toast.success('URL copiée');
                      }}
                    >
                      Copier URL
                    </button>
                    <button 
                      className="text-xs text-red-600 hover:text-red-800"
                      onClick={async () => {
                        if (confirm('Êtes-vous sûr de vouloir supprimer ce média?')) {
                          try {
                            const { error } = await supabase
                              .from('media_library')
                              .delete()
                              .eq('id', item.id);
                            
                            if (error) throw error;
                            toast.success('Média supprimé');
                            fetchMedia();
                          } catch (error) {
                            console.error('Erreur lors de la suppression:', error);
                            toast.error('Erreur lors de la suppression');
                          }
                        }
                      }}
                    >
                      Supprimer
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Tabs */}
      <div className="border-b border-gray-200 border-solid">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('content')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'content'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Type className="h-4 w-4 inline mr-2" />
            Contenu
          </button>
          <button
            onClick={() => setActiveTab('media')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'media'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Image className="h-4 w-4 inline mr-2" />
            Médiathèque
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'content' && renderContentTab()}
      {activeTab === 'media' && renderMediaTab()}
    </div>
  );
};

export default ContentManager;



