import React from 'react';
import Card from '../ui/Card';
import {
  Cake, Gift, <PERSON>Handshake, Utensils,
  Presentation as Present<PERSON><PERSON><PERSON>, Gamepad2,
  TreePine, Music, Popcorn, Palette, GlassWater,
  Castle, Ghost
} from 'lucide-react';
import { Link } from '../ui/Link';

interface ServiceCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ icon, title, description }) => {
  return (
    <Card hoverEffect className="flex flex-col items-center text-center">
      <div className="text-primary-500 p-3 bg-primary-50 rounded-full mb-4">
        {icon}
      </div>
      <h3 className="text-xl font-bold text-secondary-800 mb-2">{title}</h3>
      <p className="text-secondary-600">{description}</p>
    </Card>
  );
};

const Services = () => {
  const services = [
    {
      icon: <Cake size={28} />,
      title: "Év<PERSON><PERSON><PERSON> Enfant",
      description: "Anniversaires féeriques, baptêmes et fêtes à thème pour vos enfants."
    },
    {
      icon: <Gift size={28} />,
      title: "Événementiel Adulte",
      description: "Anniversaires élégants, soirées à thème et fêtes de fin d'année."
    },
    {
      icon: <TreePine size={28} />,
      title: "Arbre de Noël",
      description: "Décorations festives et ambiance magique pour vos célébrations de fin d'année."
    },
    {
      icon: <Castle size={28} />,
      title: "Châteaux & Bulles",
      description: "Châteaux gonflables, bulles transparentes et trampolines pour le plaisir des enfants."
    },
    {
      icon: <Ghost size={28} />,
      title: "Mascottes & Animations",
      description: "Père Noël, mascottes et nounours géants pour animer vos événements."
    },
    {
      icon: <Music size={28} />,
      title: "Sonorisation & Clowns",
      description: "Animation musicale et spectacles de clowns pour divertir petits et grands."
    },
    {
      icon: <Popcorn size={28} />,
      title: "Stands Gourmands",
      description: "Barbe à papa, popcorn, crêpes et autres délices pour régaler vos invités."
    },
    {
      icon: <Palette size={28} />,
      title: "Ateliers Créatifs",
      description: "Ateliers de coloriage et maquillage pour enfants lors de vos événements."
    },
    {
      icon: <GlassWater size={28} />,
      title: "Bar & Traiteur",
      description: "Bar à cocktails, services traiteur et bien plus pour vos réceptions."
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-b from-white to-primary-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-display text-primary-700 mb-4">Nos Services</h2>
          <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
            Découvrez notre gamme complète de services pour tous vos événements. L'assurance d'un événement réussi !
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Link to="/services" key={index}>
              <ServiceCard
                icon={service.icon}
                title={service.title}
                description={service.description}
              />
            </Link>
          ))}
        </div>

        <div className="mt-10 text-center">
          <Link to="/services">
            <button type="button" className="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full transition-colors font-medium inline-flex items-center">
              Voir tous nos services
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M5 12h14"></path>
                <path d="M12 5l7 7-7 7"></path>
              </svg>
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default Services;