import React from 'react';
import Card from '../ui/Card';
import {
  Crown, Star, Medal, Award, Gem, Sparkles,
  Users, MapPin, Truck, Phone, Gift, Music,
  Castle, Palette, Popcorn, Utensils, TreePine,
  Ghost, Gamepad2, GlassWater
} from 'lucide-react';

interface PackFeature {
  text: string;
  included: boolean;
}

interface Pack {
  name: string;
  price: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  features: PackFeature[];
  popular?: boolean;
}

interface SupplementItem {
  name: string;
  icon: React.ReactNode;
}

interface TransportZone {
  zone: string;
  price: string;
}

const PackCard: React.FC<{ pack: Pack }> = ({ pack }) => {
  return (
    <Card className={`relative ${pack.bgColor} border-2 ${pack.color} hover:shadow-xl transition-all duration-300`}>
      {pack.popular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-4 py-1 rounded-full text-sm font-bold shadow-lg">
            ⭐ POPULAIRE
          </span>
        </div>
      )}

      <div className="text-center mb-6">
        <div className={`inline-flex items-center justify-center w-16 h-16 ${pack.color.replace('border-', 'bg-').replace('-500', '-100')} rounded-full mb-4`}>
          <div className={`${pack.color.replace('border-', 'text-')}`}>
            {pack.icon}
          </div>
        </div>
        <h3 className="text-2xl font-bold text-gray-800 mb-2">{pack.name}</h3>
        <div className="text-3xl font-bold text-primary-600 mb-4">{pack.price}</div>
      </div>

      <div className="space-y-3">
        {pack.features.map((feature, index) => (
          <div key={index} className="flex items-start space-x-3">
            <div className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center mt-0.5 ${
              feature.included ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
            }`}>
              {feature.included ? '✓' : '○'}
            </div>
            <span className={`text-sm ${feature.included ? 'text-gray-700' : 'text-gray-500'}`}>
              {feature.text}
            </span>
          </div>
        ))}
      </div>

      <div className="mt-6 pt-6 border-t border-gray-200">
        <button type="button" className={`w-full py-3 px-4 rounded-lg font-semibold transition-colors ${
          pack.popular
            ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700'
            : 'bg-primary-500 text-white hover:bg-primary-600'
        }`}>
          Choisir ce pack
        </button>
      </div>
    </Card>
  );
};

const Packs = () => {
  const packs: Pack[] = [
    {
      name: "PACK BASIQUE",
      price: "200.000 FCFA",
      icon: <Sparkles size={32} />,
      color: "border-blue-500",
      bgColor: "bg-blue-50",
      features: [
        { text: "Mise en place pour 20 enfants : chaises, tables, nappes, assiettes, verres, couverts", included: true },
        { text: "Étiquettes d'eau", included: true },
        { text: "Décoration des tables selon le thème", included: true },
        { text: "Pancarte de bienvenue", included: true },
        { text: "Espace photo : mur de star dans le thème", included: true },
        { text: "Candy Bar avec 20 sacs surprises (friandises à fournir par les parents)", included: true },
        { text: "Décoration de l'espace enfants avec des ballons", included: true },
        { text: "Clown : animation et jeux", included: false },
        { text: "Sonorisation", included: false }
      ]
    },
    {
      name: "PACK BRONZE",
      price: "275.000 FCFA",
      icon: <Medal size={32} />,
      color: "border-amber-600",
      bgColor: "bg-amber-50",
      features: [
        { text: "Mise en place pour 20 enfants : chaises, tables, nappes, assiettes, verres, couverts", included: true },
        { text: "Étiquettes d'eau", included: true },
        { text: "Décoration des tables selon le thème", included: true },
        { text: "Pancarte de bienvenue", included: true },
        { text: "Espace photo : mur de star dans le thème", included: true },
        { text: "Candy Bar avec 20 sacs surprises (friandises à fournir par les parents)", included: true },
        { text: "Décoration de l'espace enfants avec des ballons", included: true },
        { text: "Clown : animation et jeux", included: true },
        { text: "Sonorisation de l'espace", included: true }
      ]
    },
    {
      name: "PACK SILVER",
      price: "325.000 FCFA",
      icon: <Award size={32} />,
      color: "border-gray-500",
      bgColor: "bg-gray-50",
      popular: true,
      features: [
        { text: "Mise en place pour 30 enfants : chaises, tables, nappes, assiettes, verres, couverts", included: true },
        { text: "Étiquettes d'eau", included: true },
        { text: "Décoration des tables selon le thème", included: true },
        { text: "Pancarte de bienvenue", included: true },
        { text: "Espace photo : mur de star dans le thème", included: true },
        { text: "Candy Bar avec 30 sacs surprises (friandises à fournir par les parents)", included: true },
        { text: "Décoration de l'espace enfants avec des ballons", included: true },
        { text: "Clown : animation et jeux", included: true },
        { text: "Sonorisation de l'espace", included: true },
        { text: "1 stand de barbe à papa", included: true }
      ]
    }
  ];

  const packsSecondRow: Pack[] = [
    {
      name: "PACK GOLD",
      price: "400.000 FCFA",
      icon: <Star size={32} />,
      color: "border-yellow-500",
      bgColor: "bg-yellow-50",
      features: [
        { text: "Mise en place pour 30 enfants : chaises, tables, nappes, assiettes, verres, couverts", included: true },
        { text: "Étiquettes d'eau", included: true },
        { text: "Décoration des tables selon le thème", included: true },
        { text: "Pancarte de bienvenue", included: true },
        { text: "Espace photo : mur de star dans le thème", included: true },
        { text: "Candy Bar avec 30 sacs surprises (friandises à fournir par les parents)", included: true },
        { text: "Décoration de l'espace enfants avec des ballons", included: true },
        { text: "Clown : animation et jeux", included: true },
        { text: "Sonorisation de l'espace", included: true },
        { text: "1 stand de barbe à papa", included: true },
        { text: "1 château gonflable", included: true }
      ]
    },
    {
      name: "PACK FAMILIAL BRONZE",
      price: "450.000 FCFA",
      icon: <Users size={32} />,
      color: "border-orange-500",
      bgColor: "bg-orange-50",
      features: [
        { text: "Mise en place pour 20 enfants : chaises, tables, nappes, assiettes, verres, couverts", included: true },
        { text: "Étiquettes d'eau", included: true },
        { text: "Décoration des tables selon le thème", included: true },
        { text: "Pancarte de bienvenue", included: true },
        { text: "Espace photo : mur de star dans le thème", included: true },
        { text: "Candy Bar avec 20 sacs surprises (friandises à fournir par les parents)", included: true },
        { text: "Décoration de l'espace enfants avec des ballons", included: true },
        { text: "Mise en place pour 30 adultes : chaises, tables, nappes, couverts", included: true },
        { text: "Décoration des tables adultes : assiettes de présentation, serviettes, centre de table, composition florale", included: true },
        { text: "Clown : animation et jeux", included: true },
        { text: "Sonorisation de l'espace", included: true }
      ]
    },
    {
      name: "PACK PREMIUM",
      price: "500.000 FCFA",
      icon: <Crown size={32} />,
      color: "border-purple-500",
      bgColor: "bg-purple-50",
      features: [
        { text: "Mise en place pour 40 enfants : chaises, tables, nappes, assiettes, verres, couverts, étiquettes", included: true },
        { text: "Décoration des tables selon le thème", included: true },
        { text: "Pancarte de bienvenue", included: true },
        { text: "Espace photo : mur de star dans le thème", included: true },
        { text: "Candy Bar avec 40 sacs surprises", included: true },
        { text: "Décoration de l'espace enfants", included: true },
        { text: "Clown : animation et jeux", included: true },
        { text: "Sonorisation", included: true },
        { text: "1 stand de barbe à papa", included: true },
        { text: "1 château gonflable", included: true },
        { text: "1 Bubble House", included: true },
        { text: "Maquillage enfant", included: true }
      ]
    }
  ];

  const supplements: SupplementItem[] = [
    { name: "Clown", icon: <Sparkles size={20} /> },
    { name: "Sonorisation + DJ + micros", icon: <Music size={20} /> },
    { name: "Mise en place et décoration adultes", icon: <Users size={20} /> },
    { name: "Château gonflable", icon: <Castle size={20} /> },
    { name: "Bubble House / Château à bulles", icon: <Gem size={20} /> },
    { name: "Chipsy personnalisés", icon: <Gift size={20} /> },
    { name: "Jus personnalisés", icon: <Gift size={20} /> },
    { name: "Brochettes de bonbons personnalisées", icon: <Gift size={20} /> },
    { name: "Maquillage enfants", icon: <Palette size={20} /> },
    { name: "Popcorn", icon: <Popcorn size={20} /> },
    { name: "Barbe à papa", icon: <Sparkles size={20} /> },
    { name: "Crêpes", icon: <Utensils size={20} /> },
    { name: "Tente Pergola en voile", icon: <TreePine size={20} /> },
    { name: "Tente perforée blanche", icon: <TreePine size={20} /> },
    { name: "Bâche", icon: <TreePine size={20} /> },
    { name: "Mascotte", icon: <Ghost size={20} /> },
    { name: "Trampoline", icon: <Gamepad2 size={20} /> },
    { name: "Brochettes de bonbons", icon: <Gift size={20} /> },
    { name: "Bar à cocktail", icon: <GlassWater size={20} /> }
  ];

  const transportZones: TransportZone[] = [
    { zone: "Cocody", price: "45.000 FCFA" },
    { zone: "Bingerville", price: "50.000 FCFA" },
    { zone: "Marcory / Treichville", price: "50.000 FCFA" },
    { zone: "Koumassi et environs", price: "50.000 FCFA" },
    { zone: "Yopougon", price: "55.000 FCFA" },
    { zone: "Bassam", price: "100.000 FCFA" },
    { zone: "Bonoua", price: "130.000 FCFA" },
    { zone: "Assinie", price: "150.000 FCFA" },
    { zone: "Au-delà", price: "à partir de 200.000 FCFA" }
  ];

  return (
    <section className="py-16 bg-gradient-to-b from-primary-50 to-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-display text-primary-700 mb-4">Nos Packs Événementiels</h2>
          <p className="text-lg text-secondary-600 max-w-3xl mx-auto">
            Choisissez le pack qui correspond parfaitement à vos besoins et à votre budget.
            Chaque pack est conçu pour vous offrir une expérience complète et mémorable.
          </p>
        </div>

        {/* Première rangée de packs */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {packs.map((pack, index) => (
            <PackCard key={index} pack={pack} />
          ))}
        </div>

        {/* Deuxième rangée de packs */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {packsSecondRow.map((pack, index) => (
            <PackCard key={index} pack={pack} />
          ))}
        </div>

        {/* Suppléments à la carte */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-12">
          <h3 className="text-2xl font-bold text-center text-primary-700 mb-8 flex items-center justify-center">
            <Gift className="mr-3" size={28} />
            Suppléments "À LA CARTE"
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {supplements.map((supplement, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div className="text-primary-500">
                  {supplement.icon}
                </div>
                <span className="text-sm text-gray-700">{supplement.name}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Frais de transport */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl shadow-lg p-8">
          <h3 className="text-2xl font-bold text-center text-primary-700 mb-8 flex items-center justify-center">
            <Truck className="mr-3" size={28} />
            Frais de transport aller-retour
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <h4 className="font-semibold text-lg text-gray-800 mb-4 flex items-center">
                <MapPin className="mr-2" size={20} />
                Abidjan
              </h4>
              <div className="space-y-2">
                {transportZones.slice(0, 5).map((zone, index) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-white rounded-lg">
                    <span className="text-gray-700">{zone.zone}</span>
                    <span className="font-semibold text-primary-600">{zone.price}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-lg text-gray-800 mb-4 flex items-center">
                <MapPin className="mr-2" size={20} />
                Hors Abidjan
              </h4>
              <div className="space-y-2">
                {transportZones.slice(5).map((zone, index) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-white rounded-lg">
                    <span className="text-gray-700">{zone.zone}</span>
                    <span className="font-semibold text-primary-600">{zone.price}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="md:col-span-2 lg:col-span-1">
              <div className="bg-primary-100 rounded-xl p-6 text-center">
                <Phone className="mx-auto mb-4 text-primary-600" size={32} />
                <h4 className="font-semibold text-lg text-primary-700 mb-2">Besoin d'un devis ?</h4>
                <p className="text-primary-600 mb-4">Contactez-nous pour un devis personnalisé</p>
                <button className="bg-primary-500 text-white px-6 py-2 rounded-lg hover:bg-primary-600 transition-colors">
                  Demander un devis
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Packs;
